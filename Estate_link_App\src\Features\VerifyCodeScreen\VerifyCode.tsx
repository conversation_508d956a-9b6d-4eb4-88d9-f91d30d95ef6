import { useState, useRef, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, StatusBar, TouchableWithoutFeedback, Keyboard, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import Ionicons from '@expo/vector-icons/Ionicons';
import { ErrorMessage } from 'components/ErrorMessage';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { verifyOTP, incrementOTPAttempts, clearError } from 'store/slices/authSlice';
import { useFormValidation } from 'hooks/useFormValidation';
import { otpSchema } from 'validation/schemas';

type RootStackParamList = {
  Login: undefined;
  ForgotPassword: undefined;
  PasswordReset: undefined;
  VerifyCode: undefined;
  SetPassword: undefined;
};

type VerifyCodeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'VerifyCode'>;

export function VerifyCode() {
  const navigation = useNavigation<VerifyCodeScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const { isLoading, error, otpData } = useAppSelector((state) => state.auth);

  const [code, setCode] = useState('');
  const [timeLeft, setTimeLeft] = useState(140); // 2:20 in seconds
  const [showSuccess, setShowSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const inputRef = useRef<TextInput>(null);

  const {
    errors,
    validateForm,
    setFieldTouched,
    getFieldError,
    isFieldTouched,
    clearErrors,
  } = useFormValidation(otpSchema);

  // Countdown timer effect
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => {
        setTimeLeft(timeLeft - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const handleCodeChange = async (value: string) => {
    // Only allow numbers and limit to reasonable OTP length
    const numericValue = value.replace(/[^0-9]/g, '');
    if (numericValue.length <= 6) {
      setCode(numericValue);
      
      // Clear Redux error when user starts typing
      if (error) {
        dispatch(clearError());
      }

      // Clear success message when user starts typing
      if (showSuccess) {
        setShowSuccess(false);
        setSuccessMessage('');
      }

      // Validate field on change
      if (isFieldTouched('otp')) {
        const fieldError = await validateForm({ otp: numericValue });
        if (fieldError.isValid) {
          // Clear field error if validation passes
        }
      }
    }
  };

  const handleBlur = () => {
    setFieldTouched('otp', true);
  };

  const handleVerify = async () => {
    // Clear previous errors
    clearErrors();
    dispatch(clearError());

    // Validate form
    const validation = await validateForm({ otp: code });
    if (!validation.isValid) {
      return;
    }

    try {
      // Verify OTP
      await dispatch(verifyOTP({ 
        email: otpData?.email || '', 
        otp: code 
      })).unwrap();
      
      // Success - show success message
      setShowSuccess(true);
      setSuccessMessage('Verification successful!');
      
      // Show success message for 2 seconds, then navigate to SetPassword screen
      setTimeout(() => {
        navigation.navigate('SetPassword');
      }, 600);
    } catch (error) {
      // Error handling is done in Redux slice
      console.error('OTP verification error:', error);
    }
  };

  const handleResend = () => {
    // Increment OTP attempts in Redux
    dispatch(incrementOTPAttempts());
    
    // Reset timer
    setTimeLeft(140);
    
    // Clear current code and messages
    setCode('');
    clearErrors();
    dispatch(clearError());
    setShowSuccess(false);
    setSuccessMessage('');
    
    // Focus on input
    inputRef.current?.focus();
  };

  const otpError = getFieldError('otp');
  const showOtpError = isFieldTouched('otp') && otpError;
  const isFormValid = code.length >= 4 && !otpError;

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Header with Back Button */}
        <View className="flex-row items-center px-6 pb-4 pt-20">
          <TouchableOpacity className="flex-row items-center" onPress={() => navigation.goBack()} disabled={isLoading}>
            <Ionicons name="chevron-back" size={18} color="#656565" style={{ marginRight: 8 }} />
            <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 18 }}>
              Back to Forgot Password
            </Text>
          </TouchableOpacity>
        </View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-8">
        {/* Logo Section */}
        <View className="items-center" style={{ marginBottom: 60 }}>
          <Image
            source={require('../../../assets/Logo.png')}
            style={{ width: 200, height: 64, marginBottom: 40 }}
            resizeMode="contain"
          />
        </View>

        {/* Title and Description */}
        <View style={{ marginBottom: 40 }}>
          <Text
            className="text-center font-oxanium-bold text-text-primary"
            style={{ fontSize: 40, fontWeight: '600', marginBottom: 20, lineHeight: 40 }}>
            Verify code
          </Text>

          <Text
            className="text-center font-oxanium-medium text-text-secondary"
            style={{ fontSize: 18, fontWeight: '400', lineHeight: 24, paddingHorizontal: 20 }}>
            An authentication code has been sent to your email.
          </Text>
        </View>

        {/* Code Input Section */}
        <View style={{ marginBottom: 32 }}>
          <Text
            className="font-oxanium-medium text-text-primary"
            style={{ fontSize: 16, fontWeight: '400', marginBottom: 16 }}>
            Enter Code
          </Text>

          <TextInput
            ref={inputRef}
            className={`rounded-lg border bg-background-input text-text-primary ${
              showOtpError ? 'border-red-500' : 'border-border'
            }`}
            style={{
              height: 56,
              borderWidth: 1,
              paddingHorizontal: 16,
              fontSize: 16,
              textAlign: 'left',
              fontFamily: 'Oxanium-Medium',
            }}
            placeholder="enter your OTP"
            placeholderTextColor="#9CA3AF"
            value={code}
            onChangeText={handleCodeChange}
            onBlur={handleBlur}
            keyboardType="number-pad"
            maxLength={6}
            autoFocus={true}
            editable={!isLoading}
          />

          {/* Error Message */}
          {showOtpError && (
            <ErrorMessage message={otpError} visible={true} />
          )}
          
          {/* Redux Error Message */}
          {error && (
            <ErrorMessage message={error} visible={true} />
          )}
          
          {/* Success Message */}
          {showSuccess && (
            <View className="mb-4 flex-row items-center">
              <View className="mr-3">
                <Text className="font-bold text-green-600" style={{ fontSize: 18 }}>
                  ✓
                </Text>
              </View>
              <Text className="flex-1 text-green-600" style={{ fontSize: 16, fontFamily: 'Oxanium-Medium' }}>
                {successMessage}
              </Text>
            </View>
          )}
        </View>

        {/* Resend Section */}
        <View className="flex-row items-center justify-between" style={{ marginBottom: 30 }}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 16 }}>
              Didn&apos;t receive a code?
            </Text>
            <TouchableOpacity onPress={handleResend} disabled={isLoading}>
              <Text
                className="font-oxanium-bold text-error-light"
                style={{ fontSize: 16, marginLeft: 6 }}>
                Resend
              </Text>
            </TouchableOpacity>
          </View>
          <View
            className="items-center justify-center rounded-full border border-error-light bg-white"
            style={{ width: 30, height: 30 }}>
            <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 12 }}>
              {formatTime(timeLeft)}
            </Text>
          </View>
        </View>

        {/* Verify Button */}
        <TouchableOpacity
          className="items-center justify-center"
          style={{
            height: 56,
            backgroundColor: isFormValid && !isLoading ? '#3C9D9B' : 'white',
            borderWidth: 2,
            borderColor: '#3C9D9B',
            borderRadius: 28,
            marginBottom: 40,
          }}
          onPress={handleVerify}
          disabled={!isFormValid || isLoading}>
          {isLoading ? (
            <ActivityIndicator color="#3C9D9B" size="small" />
          ) : (
            <Text
              className="font-oxanium-bold"
              style={{
                fontSize: 18,
                fontWeight: '600',
                color: isFormValid ? 'white' : '#3C9D9B',
              }}>
              Verify
            </Text>
          )}
        </TouchableOpacity>

        {/* Bottom Link */}
        <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
          <TouchableOpacity disabled={isLoading}>
            <Text
              className="font-oxanium-bold text-text-primary underline"
              style={{ fontSize: 16, fontWeight: '400' }}>
              Log into Estate Control
            </Text>
          </TouchableOpacity>
        </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}
