import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, StatusBar, TouchableWithoutFeedback, Keyboard, ScrollView, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import Ionicons from '@expo/vector-icons/Ionicons';
import { EyeIcon } from '../../../components/EyeIcon';
import { ErrorMessage } from '../../../components/ErrorMessage';
import { SuccessMessage } from '../../../components/SuccessMessage';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import { setNewPassword, clearError } from '../../../store/slices/authSlice';
import { useFormValidation } from '../../../hooks/useFormValidation';
import { passwordSchema } from '../../../validation/schemas';

type RootStackParamList = {
    Login: undefined;
    ForgotPassword: undefined;
    VerifyCode: undefined;
    SetPassword: undefined;
    PasswordReset: undefined;
    WelcomeBack: undefined;
    InitialScreen: undefined;
};

type SetPasswordScreenNavigationProp = StackNavigationProp<RootStackParamList, 'SetPassword'>;

export function SetPassword() {
    const navigation = useNavigation<SetPasswordScreenNavigationProp>();
    const dispatch = useAppDispatch();
    const { isLoading, error, otpData } = useAppSelector((state) => state.auth);

    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [showSuccess, setShowSuccess] = useState(false);
    const [successMessage, setSuccessMessage] = useState('');
    const [showPasswordSuccessModal, setShowPasswordSuccessModal] = useState(false);

    const {
        errors,
        validateForm,
        setFieldTouched,
        getFieldError,
        isFieldTouched,
        clearErrors,
    } = useFormValidation(passwordSchema);

    // Password validation
    const hasMinLength = password.length >= 8;
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const passwordsMatch = password === confirmPassword && password.length > 0;

    const isFormValid = hasMinLength && hasSpecialChar && passwordsMatch;

    // Button should be enabled when user has entered some text
    const hasUserInput = password.length > 0 || confirmPassword.length > 0;

    const handlePasswordChange = async (value: string) => {
        setPassword(value);
        
        // Clear Redux error when user starts typing
        if (error) {
            dispatch(clearError());
        }

        // Validate field on change
        if (isFieldTouched('password')) {
            const fieldError = await validateForm({ 
                password: value, 
                confirmPassword 
            });
            if (fieldError.isValid) {
                // Clear field error if validation passes
            }
        }
    };

    const handleConfirmPasswordChange = async (value: string) => {
        setConfirmPassword(value);
        
        // Clear Redux error when user starts typing
        if (error) {
            dispatch(clearError());
        }

        // Validate field on change
        if (isFieldTouched('confirmPassword')) {
            const fieldError = await validateForm({ 
                password, 
                confirmPassword: value 
            });
            if (fieldError.isValid) {
                // Clear field error if validation passes
            }
        }
    };

    const handlePasswordBlur = () => {
        setFieldTouched('password', true);
    };

    const handleConfirmPasswordBlur = () => {
        setFieldTouched('confirmPassword', true);
    };

    const handleSetPassword = async () => {
        // Clear previous errors
        clearErrors();
        dispatch(clearError());

        // Validate form
        const validation = await validateForm({
            password,
            confirmPassword,
        });

        if (!validation.isValid) {
            return;
        }

        try {
            // Set new password
            await dispatch(setNewPassword({ 
                email: otpData?.email || '', 
                newPassword: password 
            })).unwrap();
            
            // Show success modal
            setShowPasswordSuccessModal(true);
        } catch (error) {
            // Error handling is done in Redux slice
            console.error('Set password error:', error);
        }
    };

    const handleSuccessModalClose = () => {
        setShowPasswordSuccessModal(false);
        // Navigate to login screen after closing modal
        navigation.navigate('Login');
    };

    const passwordError = getFieldError('password');
    const confirmPasswordError = getFieldError('confirmPassword');
    const showPasswordError = isFieldTouched('password') && passwordError;
    const showConfirmPasswordError = isFieldTouched('confirmPassword') && confirmPasswordError;

    return (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
            <View className="flex-1 bg-white">
                <StatusBar barStyle="dark-content" backgroundColor="white" />

                {/* Header with Back Button */}
                <View className="flex-row items-center px-6 pb-2 pt-20">
                    <TouchableOpacity className="flex-row items-center" onPress={() => navigation.goBack()} disabled={isLoading}>
                        <Ionicons name="chevron-back" size={18} color="#656565" style={{ marginRight: 8 }} />
                        <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 18 }}>
                            Back to Forgot Password
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Main Container */}
                <View className="flex-1 px-6 pt-8">
                    {/* Logo Section */}
                    <View className="items-center" style={{ marginBottom: 20, marginTop: 35 }}>
                        <Image
                            source={require('../../../assets/Logo.png')}
                            style={{ width: 180, height: 58, marginBottom: 20 }}
                            resizeMode="contain"
                        />
                    </View>

                    {/* Title and Description */}
                    <View style={{ marginBottom: 30 }}>
                        <Text
                            className="text-center font-oxanium-bold text-text-primary"
                            style={{ fontSize: 32, fontWeight: '600', marginBottom: 12, lineHeight: 36, textAlign: 'center' }}>
                            Set a password
                        </Text>

                        <Text
                            className="text-center font-oxanium-medium text-text-secondary"
                            style={{ fontSize: 16, fontWeight: '400', lineHeight: 22, paddingHorizontal: 10, textAlign: 'center' }}>
                            Your previous password has been resented. Please set a new password for your account.
                        </Text>
                    </View>

                    {/* Create Password Section */}
                    <View style={{ marginBottom: 20 }}>
                        <Text
                            className="font-oxanium-medium text-text-primary"
                            style={{ fontSize: 16, fontWeight: '400', marginBottom: 12 }}>
                            Create Password
                        </Text>

                        <View className="relative">
                            <TextInput
                                className={`rounded-lg border bg-background-input text-text-primary ${
                                    showPasswordError ? 'border-red-500' : 'border-border'
                                }`}
                                style={{
                                    height: 52,
                                    borderWidth: 1,
                                    paddingHorizontal: 16,
                                    paddingRight: 50,
                                    fontSize: 16,
                                    fontFamily: 'Oxanium-Medium',
                                }}
                                placeholder="enter your password"
                                placeholderTextColor="#9CA3AF"
                                value={password}
                                onChangeText={handlePasswordChange}
                                onBlur={handlePasswordBlur}
                                secureTextEntry={!showPassword}
                                autoCapitalize="none"
                                editable={!isLoading}
                            />
                            <EyeIcon
                                isVisible={showPassword}
                                onPress={() => setShowPassword(!showPassword)}
                                style={{ position: 'absolute', right: 16, top: 12 }}
                            />
                        </View>

                        {showPasswordError && (
                            <ErrorMessage message={passwordError} visible={true} />
                        )}
                    </View>

                    {/* Re-type Password Section */}
                    <View style={{ marginBottom: 24 }}>
                        <Text
                            className="font-oxanium-medium text-text-primary"
                            style={{ fontSize: 16, fontWeight: '400', marginBottom: 12 }}>
                            Re-type Password
                        </Text>

                        <View className="relative">
                            <TextInput
                                className={`rounded-lg border bg-background-input text-text-primary ${
                                    showConfirmPasswordError ? 'border-red-500' : 'border-border'
                                }`}
                                style={{
                                    height: 52,
                                    borderWidth: 1,
                                    paddingHorizontal: 16,
                                    paddingRight: 50,
                                    fontSize: 16,
                                    fontFamily: 'Oxanium-Medium',
                                }}
                                placeholder="re-enter password"
                                placeholderTextColor="#9CA3AF"
                                value={confirmPassword}
                                onChangeText={handleConfirmPasswordChange}
                                onBlur={handleConfirmPasswordBlur}
                                secureTextEntry={!showConfirmPassword}
                                autoCapitalize="none"
                                editable={!isLoading}
                            />
                            <EyeIcon
                                isVisible={showConfirmPassword}
                                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                                style={{ position: 'absolute', right: 16, top: 12 }}
                            />
                        </View>

                        {showConfirmPasswordError && (
                            <ErrorMessage message={confirmPasswordError} visible={true} />
                        )}

                        {/* Redux Error Message */}
                        {error && (
                            <ErrorMessage message={error} visible={true} />
                        )}

                        {/* Success Message */}
                        {showSuccess && (
                            <View className="mb-4 flex-row items-center">
                                <View className="mr-3">
                                    <Text className="font-bold text-green-600" style={{ fontSize: 18 }}>
                                        ✓
                                    </Text>
                                </View>
                                <Text className="flex-1 text-green-600" style={{ fontSize: 16, fontFamily: 'Oxanium-Medium' }}>
                                    {successMessage}
                                </Text>
                            </View>
                        )}
                    </View>

                    {/* Password Requirements */}
                    <View style={{ marginBottom: 30 }}>
                        <View className="flex-row items-center" style={{ marginBottom: 6 }}>
                            <View
                                className="mr-3 h-2 w-2 rounded-full"
                                style={{ backgroundColor: '#3C9D9B' }}
                            />
                            <Text
                                className="font-oxanium-medium"
                                style={{ fontSize: 15, color: '#3C9D9B' }}>
                                New password form
                            </Text>
                        </View>

                        <View className="flex-row items-center" style={{ marginBottom: 6 }}>
                            <View
                                className="mr-3 h-2 w-2 rounded-full"
                                style={{ backgroundColor: '#3C9D9B' }}
                            />
                            <Text
                                className="font-oxanium-medium"
                                style={{ fontSize: 15, color: '#3C9D9B' }}>
                                Enter password twice and match
                            </Text>
                        </View>

                        <View className="flex-row items-center" style={{ marginBottom: 6 }}>
                            <View
                                className="mr-3 h-2 w-2 rounded-full"
                                style={{ backgroundColor: '#3C9D9B' }}
                            />
                            <Text
                                className="font-oxanium-medium"
                                style={{ fontSize: 15, color: '#3C9D9B' }}>
                                Password format?
                            </Text>
                        </View>

                        <View className="ml-6">
                            <View className="flex-row items-center" style={{ marginBottom: 3 }}>
                                <View
                                    className="mr-3 h-2 w-2 rounded-full"
                                    style={{ backgroundColor: hasMinLength ? '#3C9D9B' : '#9CA3AF' }}
                                />
                                <Text
                                    className="font-oxanium-medium"
                                    style={{
                                        fontSize: 15,
                                        color: hasMinLength ? '#3C9D9B' : '#9CA3AF'
                                    }}>
                                    8 characters?
                                </Text>
                            </View>

                            <View className="flex-row items-center">
                                <View
                                    className="mr-3 h-2 w-2 rounded-full"
                                    style={{ backgroundColor: hasSpecialChar ? '#3C9D9B' : '#9CA3AF' }}
                                />
                                <Text
                                    className="font-oxanium-medium"
                                    style={{
                                        fontSize: 15,
                                        color: hasSpecialChar ? '#3C9D9B' : '#9CA3AF'
                                    }}>
                                    Special letters?
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Set Password Button */}
                    <TouchableOpacity
                        className="items-center justify-center"
                        style={{
                            height: 52,
                            backgroundColor: hasUserInput && !isLoading ? '#3C9D9B' : 'white',
                            borderWidth: 2,
                            borderColor: '#3C9D9B',
                            borderRadius: 26,
                            marginBottom: 30,
                        }}
                        onPress={handleSetPassword}
                        disabled={!hasUserInput || isLoading}>
                        {isLoading ? (
                            <ActivityIndicator color="#3C9D9B" size="small" />
                        ) : (
                            <Text
                                className="font-oxanium-bold"
                                style={{
                                    fontSize: 18,
                                    fontWeight: '600',
                                    color: hasUserInput ? 'white' : '#3C9D9B',
                                }}>
                                Set password
                            </Text>
                        )}
                    </TouchableOpacity>
                </View>

                {/* Bottom Link */}
                <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
                    <TouchableOpacity disabled={isLoading}>
                        <Text
                            className="font-oxanium-bold text-text-primary underline"
                            style={{ fontSize: 16, fontWeight: '400' }}>
                            Log into Estate Control
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Success Modal */}
                <SuccessMessage
                    title="Your password has been successfully updated."
                    message="You can now securely access your account with your new credentials."
                    visible={showPasswordSuccessModal}
                    onClose={handleSuccessModalClose}
                />
            </View>
        </TouchableWithoutFeedback>
    );
}