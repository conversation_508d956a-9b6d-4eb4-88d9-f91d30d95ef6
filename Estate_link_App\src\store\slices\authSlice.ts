import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Types
export interface AuthState {
  user: {
    id?: string;
    username?: string;
    email?: string;
    phone?: string;
    isFirstLogin?: boolean;
  } | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  otpData: {
    email?: string;
    phone?: string;
    method?: 'email' | 'phone' | 'whatsapp';
    attempts: number;
    lastAttempt?: Date;
  } | null;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
  otpData: null,
};

// Async thunks
export const checkUserStatus = createAsyncThunk(
  'auth/checkUserStatus',
  async (authenticator: string) => {
    // Simulate API call
    const response = await fetch('/api/check_status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ authenticator }),
    });
    return response.json();
  }
);

export const requestOTP = createAsyncThunk(
  'auth/requestOTP',
  async ({ email, method }: { email: string; method: 'email' | 'phone' | 'whatsapp' }) => {
    // Simulate API call
    const response = await fetch('/api/forgot_password/request_otp', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, method }),
    });
    return response.json();
  }
);

export const verifyOTP = createAsyncThunk(
  'auth/verifyOTP',
  async ({ email, otp }: { email: string; otp: string }) => {
    // Simulate API call
    const response = await fetch('/api/forgot_password/verify_otp', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, otp }),
    });
    return response.json();
  }
);

export const setNewPassword = createAsyncThunk(
  'auth/setNewPassword',
  async ({ email, newPassword }: { email: string; newPassword: string }) => {
    // Simulate API call
    const response = await fetch('/api/forgot_password/set_new_password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, new_password: newPassword }),
    });
    return response.json();
  }
);

export const loginUser = createAsyncThunk(
  'auth/loginUser',
  async (credentials: { username: string; password?: string }) => {
    // Simulate API call
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(credentials),
    });
    return response.json();
  }
);

export const setPassword = createAsyncThunk(
  'auth/setPassword',
  async ({ userId, oldPassword, newPassword }: { 
    userId: string; 
    oldPassword: string; 
    newPassword: string; 
  }) => {
    // Simulate API call
    const response = await fetch('/api/set_password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ user_id: userId, old_password: oldPassword, new_password: newPassword }),
    });
    return response.json();
  }
);

// Slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setOTPData: (state, action: PayloadAction<AuthState['otpData']>) => {
      state.otpData = action.payload;
    },
    incrementOTPAttempts: (state) => {
      if (state.otpData) {
        state.otpData.attempts += 1;
        state.otpData.lastAttempt = new Date();
      }
    },
    resetOTPData: (state) => {
      state.otpData = null;
    },
    logout: (state) => {
      state.user = null;
      state.isAuthenticated = false;
      state.otpData = null;
    },
  },
  extraReducers: (builder) => {
    // Check user status
    builder
      .addCase(checkUserStatus.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkUserStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
      })
      .addCase(checkUserStatus.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to check user status';
      });

    // Request OTP
    builder
      .addCase(requestOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(requestOTP.fulfilled, (state, action) => {
        state.isLoading = false;
        state.otpData = {
          email: action.meta.arg.email,
          method: action.meta.arg.method,
          attempts: 0,
          lastAttempt: new Date(),
        };
      })
      .addCase(requestOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to request OTP';
      });

    // Verify OTP
    builder
      .addCase(verifyOTP.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyOTP.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(verifyOTP.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Invalid OTP';
      });

    // Set new password
    builder
      .addCase(setNewPassword.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(setNewPassword.fulfilled, (state) => {
        state.isLoading = false;
        state.otpData = null;
      })
      .addCase(setNewPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to set new password';
      });

    // Login user
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.isAuthenticated = true;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
      });

    // Set password
    builder
      .addCase(setPassword.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(setPassword.fulfilled, (state, action) => {
        state.isLoading = false;
        if (state.user) {
          state.user.isFirstLogin = false;
        }
      })
      .addCase(setPassword.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to set password';
      });
  },
});

export const {
  setLoading,
  setError,
  clearError,
  setOTPData,
  incrementOTPAttempts,
  resetOTPData,
  logout,
} = authSlice.actions;

export default authSlice.reducer; 