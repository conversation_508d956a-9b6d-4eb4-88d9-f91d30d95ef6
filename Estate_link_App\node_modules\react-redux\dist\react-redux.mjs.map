{"version": 3, "sources": ["../src/utils/react.ts", "../src/utils/react-is.ts", "../src/utils/warning.ts", "../src/connect/verifySubselectors.ts", "../src/connect/selectorFactory.ts", "../src/utils/bindActionCreators.ts", "../src/utils/isPlainObject.ts", "../src/utils/verifyPlainObject.ts", "../src/connect/wrapMapToProps.ts", "../src/connect/invalidArgFactory.ts", "../src/connect/mapDispatchToProps.ts", "../src/connect/mapStateToProps.ts", "../src/connect/mergeProps.ts", "../src/utils/batch.ts", "../src/utils/Subscription.ts", "../src/utils/useIsomorphicLayoutEffect.ts", "../src/utils/shallowEqual.ts", "../src/utils/hoistStatics.ts", "../src/components/Context.ts", "../src/components/connect.tsx", "../src/components/Provider.tsx", "../src/hooks/useReduxContext.ts", "../src/hooks/useStore.ts", "../src/hooks/useDispatch.ts", "../src/hooks/useSelector.ts", "../src/exports.ts"], "sourcesContent": ["import * as React from 'react'\n\nexport { React }\n", "import type { ElementType, MemoExoticComponent, ReactElement } from 'react'\nimport { React } from './react'\n\n// Directly ported from:\n// https://unpkg.com/browse/react-is@19.0.0/cjs/react-is.production.js\n// It's very possible this could change in the future, but given that\n// we only use these in `connect`, this is a low priority.\n\nexport const IS_REACT_19 = /* @__PURE__ */ React.version.startsWith('19')\n\nconst REACT_ELEMENT_TYPE = /* @__PURE__ */ Symbol.for(\n  IS_REACT_19 ? 'react.transitional.element' : 'react.element',\n)\nconst REACT_PORTAL_TYPE = /* @__PURE__ */ Symbol.for('react.portal')\nconst REACT_FRAGMENT_TYPE = /* @__PURE__ */ Symbol.for('react.fragment')\nconst REACT_STRICT_MODE_TYPE = /* @__PURE__ */ Symbol.for('react.strict_mode')\nconst REACT_PROFILER_TYPE = /* @__PURE__ */ Symbol.for('react.profiler')\nconst REACT_CONSUMER_TYPE = /* @__PURE__ */ Symbol.for('react.consumer')\nconst REACT_CONTEXT_TYPE = /* @__PURE__ */ Symbol.for('react.context')\nconst REACT_FORWARD_REF_TYPE = /* @__PURE__ */ Symbol.for('react.forward_ref')\nconst REACT_SUSPENSE_TYPE = /* @__PURE__ */ Symbol.for('react.suspense')\nconst REACT_SUSPENSE_LIST_TYPE = /* @__PURE__ */ Symbol.for(\n  'react.suspense_list',\n)\nconst REACT_MEMO_TYPE = /* @__PURE__ */ Symbol.for('react.memo')\nconst REACT_LAZY_TYPE = /* @__PURE__ */ Symbol.for('react.lazy')\nconst REACT_OFFSCREEN_TYPE = /* @__PURE__ */ Symbol.for('react.offscreen')\nconst REACT_CLIENT_REFERENCE = /* @__PURE__ */ Symbol.for(\n  'react.client.reference',\n)\n\nexport const ForwardRef = REACT_FORWARD_REF_TYPE\nexport const Memo = REACT_MEMO_TYPE\n\nexport function isValidElementType(type: any): type is ElementType {\n  return typeof type === 'string' ||\n    typeof type === 'function' ||\n    type === REACT_FRAGMENT_TYPE ||\n    type === REACT_PROFILER_TYPE ||\n    type === REACT_STRICT_MODE_TYPE ||\n    type === REACT_SUSPENSE_TYPE ||\n    type === REACT_SUSPENSE_LIST_TYPE ||\n    type === REACT_OFFSCREEN_TYPE ||\n    (typeof type === 'object' &&\n      type !== null &&\n      (type.$$typeof === REACT_LAZY_TYPE ||\n        type.$$typeof === REACT_MEMO_TYPE ||\n        type.$$typeof === REACT_CONTEXT_TYPE ||\n        type.$$typeof === REACT_CONSUMER_TYPE ||\n        type.$$typeof === REACT_FORWARD_REF_TYPE ||\n        type.$$typeof === REACT_CLIENT_REFERENCE ||\n        type.getModuleId !== undefined))\n    ? !0\n    : !1\n}\n\nfunction typeOf(object: any): symbol | undefined {\n  if (typeof object === 'object' && object !== null) {\n    const { $$typeof } = object\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        switch (((object = object.type), object)) {\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n          case REACT_SUSPENSE_LIST_TYPE:\n            return object\n          default:\n            switch (((object = object && object.$$typeof), object)) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n                return object\n              case REACT_CONSUMER_TYPE:\n                return object\n              default:\n                return $$typeof\n            }\n        }\n      case REACT_PORTAL_TYPE:\n        return $$typeof\n    }\n  }\n}\n\nexport function isContextConsumer(object: any): object is ReactElement {\n  return IS_REACT_19\n    ? typeOf(object) === REACT_CONSUMER_TYPE\n    : typeOf(object) === REACT_CONTEXT_TYPE\n}\n\nexport function isMemo(object: any): object is MemoExoticComponent<any> {\n  return typeOf(object) === REACT_MEMO_TYPE\n}\n", "/**\r\n * Prints a warning in the console if it exists.\r\n *\r\n * @param {String} message The warning message.\r\n * @returns {void}\r\n */\r\nexport default function warning(message: string) {\r\n  /* eslint-disable no-console */\r\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\r\n    console.error(message)\r\n  }\r\n  /* eslint-enable no-console */\r\n  try {\r\n    // This error was thrown as a convenience so that if you enable\r\n    // \"break on all exceptions\" in your console,\r\n    // it would pause the execution at this line.\r\n    throw new Error(message)\r\n    /* eslint-disable no-empty */\r\n  } catch (e) {}\r\n  /* eslint-enable no-empty */\r\n}\r\n", "import warning from '../utils/warning'\n\nfunction verify(selector: unknown, methodName: string): void {\n  if (!selector) {\n    throw new Error(`Unexpected value for ${methodName} in connect.`)\n  } else if (\n    methodName === 'mapStateToProps' ||\n    methodName === 'mapDispatchToProps'\n  ) {\n    if (!Object.prototype.hasOwnProperty.call(selector, 'dependsOnOwnProps')) {\n      warning(\n        `The selector for ${methodName} of connect did not specify a value for dependsOnOwnProps.`,\n      )\n    }\n  }\n}\n\nexport default function verifySubselectors(\n  mapStateToProps: unknown,\n  mapDispatchToProps: unknown,\n  mergeProps: unknown,\n): void {\n  verify(mapStateToProps, 'mapStateToProps')\n  verify(mapDispatchToProps, 'mapDispatchToProps')\n  verify(mergeProps, 'mergeProps')\n}\n", "import type { Dispatch, Action } from 'redux'\nimport type { ComponentType } from 'react'\nimport verifySubselectors from './verifySubselectors'\nimport type { EqualityFn, ExtendedEqualityFn } from '../types'\n\nexport type SelectorFactory<S, TProps, TOwnProps, TFactoryOptions> = (\n  dispatch: Dispatch<Action<string>>,\n  factoryOptions: TFactoryOptions,\n) => Selector<S, TProps, TOwnProps>\n\nexport type Selector<S, TProps, TOwnProps = null> = TOwnProps extends\n  | null\n  | undefined\n  ? (state: S) => TProps\n  : (state: S, ownProps: TOwnProps) => TProps\n\nexport type MapStateToProps<TStateProps, TOwnProps, State> = (\n  state: State,\n  ownProps: TOwnProps,\n) => TStateProps\n\nexport type MapStateToPropsFactory<TStateProps, TOwnProps, State> = (\n  initialState: State,\n  ownProps: TOwnProps,\n) => MapStateToProps<TStateProps, TOwnProps, State>\n\nexport type MapStateToPropsParam<TStateProps, TOwnProps, State> =\n  | MapStateToPropsFactory<TStateProps, TOwnProps, State>\n  | MapStateToProps<TStateProps, TOwnProps, State>\n  | null\n  | undefined\n\nexport type MapDispatchToPropsFunction<TDispatchProps, TOwnProps> = (\n  dispatch: Dispatch<Action<string>>,\n  ownProps: TOwnProps,\n) => TDispatchProps\n\nexport type MapDispatchToProps<TDispatchProps, TOwnProps> =\n  | MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\n  | TDispatchProps\n\nexport type MapDispatchToPropsFactory<TDispatchProps, TOwnProps> = (\n  dispatch: Dispatch<Action<string>>,\n  ownProps: TOwnProps,\n) => MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\n\nexport type MapDispatchToPropsParam<TDispatchProps, TOwnProps> =\n  | MapDispatchToPropsFactory<TDispatchProps, TOwnProps>\n  | MapDispatchToProps<TDispatchProps, TOwnProps>\n\nexport type MapDispatchToPropsNonObject<TDispatchProps, TOwnProps> =\n  | MapDispatchToPropsFactory<TDispatchProps, TOwnProps>\n  | MapDispatchToPropsFunction<TDispatchProps, TOwnProps>\n\nexport type MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps> = (\n  stateProps: TStateProps,\n  dispatchProps: TDispatchProps,\n  ownProps: TOwnProps,\n) => TMergedProps\n\ninterface PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State> {\n  readonly areStatesEqual: ExtendedEqualityFn<State, TOwnProps>\n  readonly areStatePropsEqual: EqualityFn<TStateProps>\n  readonly areOwnPropsEqual: EqualityFn<TOwnProps>\n}\n\nfunction pureFinalPropsSelectorFactory<\n  TStateProps,\n  TOwnProps,\n  TDispatchProps,\n  TMergedProps,\n  State,\n>(\n  mapStateToProps: WrappedMapStateToProps<TStateProps, TOwnProps, State>,\n  mapDispatchToProps: WrappedMapDispatchToProps<TDispatchProps, TOwnProps>,\n  mergeProps: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\n  dispatch: Dispatch<Action<string>>,\n  {\n    areStatesEqual,\n    areOwnPropsEqual,\n    areStatePropsEqual,\n  }: PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State>,\n) {\n  let hasRunAtLeastOnce = false\n  let state: State\n  let ownProps: TOwnProps\n  let stateProps: TStateProps\n  let dispatchProps: TDispatchProps\n  let mergedProps: TMergedProps\n\n  function handleFirstCall(firstState: State, firstOwnProps: TOwnProps) {\n    state = firstState\n    ownProps = firstOwnProps\n    stateProps = mapStateToProps(state, ownProps)\n    dispatchProps = mapDispatchToProps(dispatch, ownProps)\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n    hasRunAtLeastOnce = true\n    return mergedProps\n  }\n\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps)\n\n    if (mapDispatchToProps.dependsOnOwnProps)\n      dispatchProps = mapDispatchToProps(dispatch, ownProps)\n\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n    return mergedProps\n  }\n\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps)\n      stateProps = mapStateToProps(state, ownProps)\n\n    if (mapDispatchToProps.dependsOnOwnProps)\n      dispatchProps = mapDispatchToProps(dispatch, ownProps)\n\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n    return mergedProps\n  }\n\n  function handleNewState() {\n    const nextStateProps = mapStateToProps(state, ownProps)\n    const statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps)\n    stateProps = nextStateProps\n\n    if (statePropsChanged)\n      mergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n\n    return mergedProps\n  }\n\n  function handleSubsequentCalls(nextState: State, nextOwnProps: TOwnProps) {\n    const propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps)\n    const stateChanged = !areStatesEqual(\n      nextState,\n      state,\n      nextOwnProps,\n      ownProps,\n    )\n    state = nextState\n    ownProps = nextOwnProps\n\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState()\n    if (propsChanged) return handleNewProps()\n    if (stateChanged) return handleNewState()\n    return mergedProps\n  }\n\n  return function pureFinalPropsSelector(\n    nextState: State,\n    nextOwnProps: TOwnProps,\n  ) {\n    return hasRunAtLeastOnce\n      ? handleSubsequentCalls(nextState, nextOwnProps)\n      : handleFirstCall(nextState, nextOwnProps)\n  }\n}\n\ninterface WrappedMapStateToProps<TStateProps, TOwnProps, State> {\n  (state: State, ownProps: TOwnProps): TStateProps\n  readonly dependsOnOwnProps: boolean\n}\n\ninterface WrappedMapDispatchToProps<TDispatchProps, TOwnProps> {\n  (dispatch: Dispatch<Action<string>>, ownProps: TOwnProps): TDispatchProps\n  readonly dependsOnOwnProps: boolean\n}\n\nexport interface InitOptions<TStateProps, TOwnProps, TMergedProps, State>\n  extends PureSelectorFactoryComparisonOptions<TStateProps, TOwnProps, State> {\n  readonly shouldHandleStateChanges: boolean\n  readonly displayName: string\n  readonly wrappedComponentName: string\n  readonly WrappedComponent: ComponentType<TOwnProps>\n  readonly areMergedPropsEqual: EqualityFn<TMergedProps>\n}\n\nexport interface SelectorFactoryOptions<\n  TStateProps,\n  TOwnProps,\n  TDispatchProps,\n  TMergedProps,\n  State,\n> extends InitOptions<TStateProps, TOwnProps, TMergedProps, State> {\n  readonly initMapStateToProps: (\n    dispatch: Dispatch<Action<string>>,\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>,\n  ) => WrappedMapStateToProps<TStateProps, TOwnProps, State>\n  readonly initMapDispatchToProps: (\n    dispatch: Dispatch<Action<string>>,\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>,\n  ) => WrappedMapDispatchToProps<TDispatchProps, TOwnProps>\n  readonly initMergeProps: (\n    dispatch: Dispatch<Action<string>>,\n    options: InitOptions<TStateProps, TOwnProps, TMergedProps, State>,\n  ) => MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>\n}\n\n// TODO: Add more comments\n\n// The selector returned by selectorFactory will memoize its results,\n// allowing connect's shouldComponentUpdate to return false if final\n// props have not changed.\n\nexport default function finalPropsSelectorFactory<\n  TStateProps,\n  TOwnProps,\n  TDispatchProps,\n  TMergedProps,\n  State,\n>(\n  dispatch: Dispatch<Action<string>>,\n  {\n    initMapStateToProps,\n    initMapDispatchToProps,\n    initMergeProps,\n    ...options\n  }: SelectorFactoryOptions<\n    TStateProps,\n    TOwnProps,\n    TDispatchProps,\n    TMergedProps,\n    State\n  >,\n) {\n  const mapStateToProps = initMapStateToProps(dispatch, options)\n  const mapDispatchToProps = initMapDispatchToProps(dispatch, options)\n  const mergeProps = initMergeProps(dispatch, options)\n\n  if (process.env.NODE_ENV !== 'production') {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps)\n  }\n\n  return pureFinalPropsSelectorFactory<\n    TStateProps,\n    TOwnProps,\n    TDispatchProps,\n    TMergedProps,\n    State\n  >(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options)\n}\n", "import type { ActionCreatorsMapObject, Dispatch } from 'redux'\n\nexport default function bindActionCreators(\n  actionCreators: ActionCreatorsMapObject,\n  dispatch: Dispatch,\n): ActionCreatorsMapObject {\n  const boundActionCreators: ActionCreatorsMapObject = {}\n\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key]\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = (...args) => dispatch(actionCreator(...args))\n    }\n  }\n  return boundActionCreators\n}\n", "/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nexport default function isPlainObject(obj: unknown) {\n  if (typeof obj !== 'object' || obj === null) return false\n\n  const proto = Object.getPrototypeOf(obj)\n  if (proto === null) return true\n\n  let baseProto = proto\n  while (Object.getPrototypeOf(baseProto) !== null) {\n    baseProto = Object.getPrototypeOf(baseProto)\n  }\n\n  return proto === baseProto\n}\n", "import isPlainObject from './isPlainObject'\nimport warning from './warning'\n\nexport default function verifyPlainObject(\n  value: unknown,\n  displayName: string,\n  methodName: string,\n) {\n  if (!isPlainObject(value)) {\n    warning(\n      `${methodName}() in ${displayName} must return a plain object. Instead received ${value}.`,\n    )\n  }\n}\n", "import type { ActionCreatorsMapObject, Dispatch, ActionCreator } from 'redux'\n\nimport type { FixTypeLater } from '../types'\nimport verifyPlainObject from '../utils/verifyPlainObject'\n\ntype AnyState = { [key: string]: any }\ntype StateOrDispatch<S extends AnyState = AnyState> = S | Dispatch\n\ntype AnyProps = { [key: string]: any }\n\nexport type MapToProps<P extends AnyProps = AnyProps> = {\n  // eslint-disable-next-line no-unused-vars\n  (stateOrDispatch: StateOrDispatch, ownProps?: P): FixTypeLater\n  dependsOnOwnProps?: boolean\n}\n\nexport function wrapMapToPropsConstant(\n  // * Note:\n  //  It seems that the dispatch argument\n  //  could be a dispatch function in some cases (ex: whenMapDispatchToPropsIsMissing)\n  //  and a state object in some others (ex: whenMapStateToPropsIsMissing)\n  // eslint-disable-next-line no-unused-vars\n  getConstant: (dispatch: Dispatch) =>\n    | {\n        dispatch?: Dispatch\n        dependsOnOwnProps?: boolean\n      }\n    | ActionCreatorsMapObject\n    | ActionCreator<any>,\n) {\n  return function initConstantSelector(dispatch: Dispatch) {\n    const constant = getConstant(dispatch)\n\n    function constantSelector() {\n      return constant\n    }\n    constantSelector.dependsOnOwnProps = false\n    return constantSelector\n  }\n}\n\n// dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\n// whether mapToProps needs to be invoked when props have changed.\n//\n// A length of one signals that mapToProps does not depend on props from the parent component.\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\n// therefore not reporting its length accurately..\n// TODO Can this get pulled out so that we can subscribe directly to the store if we don't need ownProps?\nfunction getDependsOnOwnProps(mapToProps: MapToProps) {\n  return mapToProps.dependsOnOwnProps\n    ? Boolean(mapToProps.dependsOnOwnProps)\n    : mapToProps.length !== 1\n}\n\n// Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\n// this function wraps mapToProps in a proxy function which does several things:\n//\n//  * Detects whether the mapToProps function being called depends on props, which\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\n//\n//  * On first call, handles mapToProps if returns another function, and treats that\n//    new function as the true mapToProps for subsequent calls.\n//\n//  * On first call, verifies the first result is a plain object, in order to warn\n//    the developer that their mapToProps function is not returning a valid result.\n//\nexport function wrapMapToPropsFunc<P extends AnyProps = AnyProps>(\n  mapToProps: MapToProps,\n  methodName: string,\n) {\n  return function initProxySelector(\n    dispatch: Dispatch,\n    { displayName }: { displayName: string },\n  ) {\n    const proxy = function mapToPropsProxy(\n      stateOrDispatch: StateOrDispatch,\n      ownProps?: P,\n    ): MapToProps {\n      return proxy.dependsOnOwnProps\n        ? proxy.mapToProps(stateOrDispatch, ownProps)\n        : proxy.mapToProps(stateOrDispatch, undefined)\n    }\n\n    // allow detectFactoryAndVerify to get ownProps\n    proxy.dependsOnOwnProps = true\n\n    proxy.mapToProps = function detectFactoryAndVerify(\n      stateOrDispatch: StateOrDispatch,\n      ownProps?: P,\n    ): MapToProps {\n      proxy.mapToProps = mapToProps\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps)\n      let props = proxy(stateOrDispatch, ownProps)\n\n      if (typeof props === 'function') {\n        proxy.mapToProps = props\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props)\n        props = proxy(stateOrDispatch, ownProps)\n      }\n\n      if (process.env.NODE_ENV !== 'production')\n        verifyPlainObject(props, displayName, methodName)\n\n      return props\n    }\n\n    return proxy\n  }\n}\n", "import type { Action, Dispatch } from 'redux'\n\nexport function createInvalidArgFactory(arg: unknown, name: string) {\n  return (\n    dispatch: Dispatch<Action<string>>,\n    options: { readonly wrappedComponentName: string },\n  ) => {\n    throw new Error(\n      `Invalid value of type ${typeof arg} for ${name} argument when connecting component ${\n        options.wrappedComponentName\n      }.`,\n    )\n  }\n}\n", "import type { Action, Dispatch } from 'redux'\nimport bindActionCreators from '../utils/bindActionCreators'\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps'\nimport { createInvalidArgFactory } from './invalidArgFactory'\nimport type { MapDispatchToPropsParam } from './selectorFactory'\n\nexport function mapDispatchToPropsFactory<TDispatchProps, TOwnProps>(\n  mapDispatchToProps:\n    | MapDispatchToPropsParam<TDispatchProps, TOwnProps>\n    | undefined,\n) {\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object'\n    ? wrapMapToPropsConstant((dispatch: Dispatch<Action<string>>) =>\n        // @ts-ignore\n        bindActionCreators(mapDispatchToProps, dispatch),\n      )\n    : !mapDispatchToProps\n      ? wrapMapToPropsConstant((dispatch: Dispatch<Action<string>>) => ({\n          dispatch,\n        }))\n      : typeof mapDispatchToProps === 'function'\n        ? // @ts-ignore\n          wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps')\n        : createInvalidArgFactory(mapDispatchToProps, 'mapDispatchToProps')\n}\n", "import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps'\nimport { createInvalidArgFactory } from './invalidArgFactory'\nimport type { MapStateToPropsParam } from './selectorFactory'\n\nexport function mapStateToPropsFactory<TStateProps, TOwnProps, State>(\n  mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n) {\n  return !mapStateToProps\n    ? wrapMapToPropsConstant(() => ({}))\n    : typeof mapStateToProps === 'function'\n      ? // @ts-ignore\n        wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps')\n      : createInvalidArgFactory(mapStateToProps, 'mapStateToProps')\n}\n", "import type { Action, Dispatch } from 'redux'\nimport verifyPlainObject from '../utils/verifyPlainObject'\nimport { createInvalidArgFactory } from './invalidArgFactory'\nimport type { MergeProps } from './selectorFactory'\nimport type { EqualityFn } from '../types'\n\nfunction defaultMergeProps<\n  TStateProps,\n  TDispatchProps,\n  TOwnProps,\n  TMergedProps,\n>(\n  stateProps: TStateProps,\n  dispatchProps: TDispatchProps,\n  ownProps: TOwnProps,\n): TMergedProps {\n  // @ts-ignore\n  return { ...ownProps, ...stateProps, ...dispatchProps }\n}\n\nfunction wrapMergePropsFunc<\n  TStateProps,\n  TDispatchProps,\n  TOwnProps,\n  TMergedProps,\n>(\n  mergeProps: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\n): (\n  dispatch: Dispatch<Action<string>>,\n  options: {\n    readonly displayName: string\n    readonly areMergedPropsEqual: EqualityFn<TMergedProps>\n  },\n) => MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps> {\n  return function initMergePropsProxy(\n    dispatch,\n    { displayName, areMergedPropsEqual },\n  ) {\n    let hasRunOnce = false\n    let mergedProps: TMergedProps\n\n    return function mergePropsProxy(\n      stateProps: TStateProps,\n      dispatchProps: TDispatchProps,\n      ownProps: TOwnProps,\n    ) {\n      const nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps)\n\n      if (hasRunOnce) {\n        if (!areMergedPropsEqual(nextMergedProps, mergedProps))\n          mergedProps = nextMergedProps\n      } else {\n        hasRunOnce = true\n        mergedProps = nextMergedProps\n\n        if (process.env.NODE_ENV !== 'production')\n          verifyPlainObject(mergedProps, displayName, 'mergeProps')\n      }\n\n      return mergedProps\n    }\n  }\n}\n\nexport function mergePropsFactory<\n  TStateProps,\n  TDispatchProps,\n  TOwnProps,\n  TMergedProps,\n>(\n  mergeProps?: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\n) {\n  return !mergeProps\n    ? () => defaultMergeProps\n    : typeof mergeProps === 'function'\n      ? wrapMergePropsFunc(mergeProps)\n      : createInvalidArgFactory(mergeProps, 'mergeProps')\n}\n", "// Default to a dummy \"batch\" implementation that just runs the callback\r\nexport function defaultNoopBatch(callback: () => void) {\r\n  callback()\r\n}\r\n", "import { defaultNoopBatch as batch } from './batch'\n\n// encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\ntype VoidFunc = () => void\n\ntype Listener = {\n  callback: VoidFunc\n  next: Listener | null\n  prev: Listener | null\n}\n\nfunction createListenerCollection() {\n  let first: Listener | null = null\n  let last: Listener | null = null\n\n  return {\n    clear() {\n      first = null\n      last = null\n    },\n\n    notify() {\n      batch(() => {\n        let listener = first\n        while (listener) {\n          listener.callback()\n          listener = listener.next\n        }\n      })\n    },\n\n    get() {\n      const listeners: Listener[] = []\n      let listener = first\n      while (listener) {\n        listeners.push(listener)\n        listener = listener.next\n      }\n      return listeners\n    },\n\n    subscribe(callback: () => void) {\n      let isSubscribed = true\n\n      const listener: Listener = (last = {\n        callback,\n        next: null,\n        prev: last,\n      })\n\n      if (listener.prev) {\n        listener.prev.next = listener\n      } else {\n        first = listener\n      }\n\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return\n        isSubscribed = false\n\n        if (listener.next) {\n          listener.next.prev = listener.prev\n        } else {\n          last = listener.prev\n        }\n        if (listener.prev) {\n          listener.prev.next = listener.next\n        } else {\n          first = listener.next\n        }\n      }\n    },\n  }\n}\n\ntype ListenerCollection = ReturnType<typeof createListenerCollection>\n\nexport interface Subscription {\n  addNestedSub: (listener: VoidFunc) => VoidFunc\n  notifyNestedSubs: VoidFunc\n  handleChangeWrapper: VoidFunc\n  isSubscribed: () => boolean\n  onStateChange?: VoidFunc | null\n  trySubscribe: VoidFunc\n  tryUnsubscribe: VoidFunc\n  getListeners: () => ListenerCollection\n}\n\nconst nullListeners = {\n  notify() {},\n  get: () => [],\n} as unknown as ListenerCollection\n\nexport function createSubscription(store: any, parentSub?: Subscription) {\n  let unsubscribe: VoidFunc | undefined\n  let listeners: ListenerCollection = nullListeners\n\n  // Reasons to keep the subscription active\n  let subscriptionsAmount = 0\n\n  // Is this specific subscription subscribed (or only nested ones?)\n  let selfSubscribed = false\n\n  function addNestedSub(listener: () => void) {\n    trySubscribe()\n\n    const cleanupListener = listeners.subscribe(listener)\n\n    // cleanup nested sub\n    let removed = false\n    return () => {\n      if (!removed) {\n        removed = true\n        cleanupListener()\n        tryUnsubscribe()\n      }\n    }\n  }\n\n  function notifyNestedSubs() {\n    listeners.notify()\n  }\n\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange()\n    }\n  }\n\n  function isSubscribed() {\n    return selfSubscribed\n  }\n\n  function trySubscribe() {\n    subscriptionsAmount++\n    if (!unsubscribe) {\n      unsubscribe = parentSub\n        ? parentSub.addNestedSub(handleChangeWrapper)\n        : store.subscribe(handleChangeWrapper)\n\n      listeners = createListenerCollection()\n    }\n  }\n\n  function tryUnsubscribe() {\n    subscriptionsAmount--\n    if (unsubscribe && subscriptionsAmount === 0) {\n      unsubscribe()\n      unsubscribe = undefined\n      listeners.clear()\n      listeners = nullListeners\n    }\n  }\n\n  function trySubscribeSelf() {\n    if (!selfSubscribed) {\n      selfSubscribed = true\n      trySubscribe()\n    }\n  }\n\n  function tryUnsubscribeSelf() {\n    if (selfSubscribed) {\n      selfSubscribed = false\n      tryUnsubscribe()\n    }\n  }\n\n  const subscription: Subscription = {\n    addNestedSub,\n    notifyNestedSubs,\n    handleChangeWrapper,\n    isSubscribed,\n    trySubscribe: trySubscribeSelf,\n    tryUnsubscribe: tryUnsubscribeSelf,\n    getListeners: () => listeners,\n  }\n\n  return subscription\n}\n", "import { React } from '../utils/react'\n\n// <PERSON>act currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\n// subscription callback always has the selector from the latest render commit\n// available, otherwise a store update may happen between render and the effect,\n// which may cause missed updates; we also must ensure the store subscription\n// is created synchronously, otherwise a store update may occur before the\n// subscription is created and an inconsistent state may be observed\n\n// Matches logic in React's `shared/ExecutionEnvironment` file\nconst canUseDOM = () =>\n  !!(\n    typeof window !== 'undefined' &&\n    typeof window.document !== 'undefined' &&\n    typeof window.document.createElement !== 'undefined'\n  )\n\nconst isDOM = /* @__PURE__ */ canUseDOM()\n\n// Under React Native, we know that we always want to use useLayoutEffect\n\n/**\n * Checks if the code is running in a React Native environment.\n *\n * @returns Whether the code is running in a React Native environment.\n *\n * @see {@link https://github.com/facebook/react-native/issues/1331 Reference}\n */\nconst isRunningInReactNative = () =>\n  typeof navigator !== 'undefined' && navigator.product === 'ReactNative'\n\nconst isReactNative = /* @__PURE__ */ isRunningInReactNative()\n\nconst getUseIsomorphicLayoutEffect = () =>\n  isDOM || isReactNative ? React.useLayoutEffect : React.useEffect\n\nexport const useIsomorphicLayoutEffect =\n  /* @__PURE__ */ getUseIsomorphicLayoutEffect()\n", "function is(x: unknown, y: unknown) {\r\n  if (x === y) {\r\n    return x !== 0 || y !== 0 || 1 / x === 1 / y\r\n  } else {\r\n    return x !== x && y !== y\r\n  }\r\n}\r\n\r\nexport default function shallowEqual(objA: any, objB: any) {\r\n  if (is(objA, objB)) return true\r\n\r\n  if (\r\n    typeof objA !== 'object' ||\r\n    objA === null ||\r\n    typeof objB !== 'object' ||\r\n    objB === null\r\n  ) {\r\n    return false\r\n  }\r\n\r\n  const keysA = Object.keys(objA)\r\n  const keysB = Object.keys(objB)\r\n\r\n  if (keysA.length !== keysB.length) return false\r\n\r\n  for (let i = 0; i < keysA.length; i++) {\r\n    if (\r\n      !Object.prototype.hasOwnProperty.call(objB, keysA[i]) ||\r\n      !is(objA[keysA[i]], objB[keysA[i]])\r\n    ) {\r\n      return false\r\n    }\r\n  }\r\n\r\n  return true\r\n}\r\n", "// Copied directly from:\n// https://github.com/mridgway/hoist-non-react-statics/blob/main/src/index.js\n// https://unpkg.com/browse/@types/hoist-non-react-statics@3.3.6/index.d.ts\n\n/**\n * Copyright 2015, Yahoo! Inc.\n * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.\n */\nimport type { ForwardRefExoticComponent, MemoExoticComponent } from 'react'\nimport { ForwardRef, Memo, isMemo } from '../utils/react-is'\n\nconst REACT_STATICS = {\n  childContextTypes: true,\n  contextType: true,\n  contextTypes: true,\n  defaultProps: true,\n  displayName: true,\n  getDefaultProps: true,\n  getDerivedStateFromError: true,\n  getDerivedStateFromProps: true,\n  mixins: true,\n  propTypes: true,\n  type: true,\n} as const\n\nconst KNOWN_STATICS = {\n  name: true,\n  length: true,\n  prototype: true,\n  caller: true,\n  callee: true,\n  arguments: true,\n  arity: true,\n} as const\n\nconst FORWARD_REF_STATICS = {\n  $$typeof: true,\n  render: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n} as const\n\nconst MEMO_STATICS = {\n  $$typeof: true,\n  compare: true,\n  defaultProps: true,\n  displayName: true,\n  propTypes: true,\n  type: true,\n} as const\n\nconst TYPE_STATICS = {\n  [ForwardRef]: FORWARD_REF_STATICS,\n  [Memo]: MEMO_STATICS,\n} as const\n\nfunction getStatics(component: any) {\n  // React v16.11 and below\n  if (isMemo(component)) {\n    return MEMO_STATICS\n  }\n\n  // React v16.12 and above\n  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS\n}\n\nexport type NonReactStatics<\n  Source,\n  C extends {\n    [key: string]: true\n  } = {},\n> = {\n  [key in Exclude<\n    keyof Source,\n    Source extends MemoExoticComponent<any>\n      ? keyof typeof MEMO_STATICS | keyof C\n      : Source extends ForwardRefExoticComponent<any>\n        ? keyof typeof FORWARD_REF_STATICS | keyof C\n        : keyof typeof REACT_STATICS | keyof typeof KNOWN_STATICS | keyof C\n  >]: Source[key]\n}\n\nconst defineProperty = Object.defineProperty\nconst getOwnPropertyNames = Object.getOwnPropertyNames\nconst getOwnPropertySymbols = Object.getOwnPropertySymbols\nconst getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor\nconst getPrototypeOf = Object.getPrototypeOf\nconst objectPrototype = Object.prototype\n\nexport default function hoistNonReactStatics<\n  Target,\n  Source,\n  CustomStatic extends {\n    [key: string]: true\n  } = {},\n>(\n  targetComponent: Target,\n  sourceComponent: Source,\n): Target & NonReactStatics<Source, CustomStatic> {\n  if (typeof sourceComponent !== 'string') {\n    // don't hoist over string (html) components\n\n    if (objectPrototype) {\n      const inheritedComponent = getPrototypeOf(sourceComponent)\n      if (inheritedComponent && inheritedComponent !== objectPrototype) {\n        hoistNonReactStatics(targetComponent, inheritedComponent)\n      }\n    }\n\n    let keys: (string | symbol)[] = getOwnPropertyNames(sourceComponent)\n\n    if (getOwnPropertySymbols) {\n      keys = keys.concat(getOwnPropertySymbols(sourceComponent))\n    }\n\n    const targetStatics = getStatics(targetComponent)\n    const sourceStatics = getStatics(sourceComponent)\n\n    for (let i = 0; i < keys.length; ++i) {\n      const key = keys[i]\n      if (\n        !KNOWN_STATICS[key as keyof typeof KNOWN_STATICS] &&\n        !(sourceStatics && sourceStatics[key as keyof typeof sourceStatics]) &&\n        !(targetStatics && targetStatics[key as keyof typeof targetStatics])\n      ) {\n        const descriptor = getOwnPropertyDescriptor(sourceComponent, key)\n        try {\n          // Avoid failures from read-only properties\n          defineProperty(targetComponent, key, descriptor!)\n        } catch (e) {\n          // ignore\n        }\n      }\n    }\n  }\n\n  return targetComponent as any\n}\n", "import type { Context } from 'react'\nimport { React } from '../utils/react'\nimport type { Action, Store, UnknownAction } from 'redux'\nimport type { Subscription } from '../utils/Subscription'\nimport type { ProviderProps } from './Provider'\n\nexport interface ReactReduxContextValue<\n  SS = any,\n  A extends Action<string> = UnknownAction,\n> extends Pick<ProviderProps, 'stabilityCheck' | 'identityFunctionCheck'> {\n  store: Store<SS, A>\n  subscription: Subscription\n  getServerState?: () => SS\n}\n\nconst ContextKey = /* @__PURE__ */ Symbol.for(`react-redux-context`)\nconst gT: {\n  [ContextKey]?: Map<\n    typeof React.createContext,\n    Context<ReactReduxContextValue | null>\n  >\n} = (\n  typeof globalThis !== 'undefined'\n    ? globalThis\n    : /* fall back to a per-module scope (pre-8.1 behaviour) if `globalThis` is not available */ {}\n) as any\n\nfunction getContext(): Context<ReactReduxContextValue | null> {\n  if (!React.createContext) return {} as any\n\n  const contextMap = (gT[ContextKey] ??= new Map<\n    typeof React.createContext,\n    Context<ReactReduxContextValue | null>\n  >())\n  let realContext = contextMap.get(React.createContext)\n  if (!realContext) {\n    realContext = React.createContext<ReactReduxContextValue | null>(\n      null as any,\n    )\n    if (process.env.NODE_ENV !== 'production') {\n      realContext.displayName = 'ReactRedux'\n    }\n    contextMap.set(React.createContext, realContext)\n  }\n  return realContext\n}\n\nexport const ReactReduxContext = /*#__PURE__*/ getContext()\n\nexport type ReactReduxContextInstance = typeof ReactReduxContext\n", "/* eslint-disable valid-jsdoc, @typescript-eslint/no-unused-vars */\nimport type { ComponentType } from 'react'\nimport { React } from '../utils/react'\nimport { isValidElementType, isContextConsumer } from '../utils/react-is'\n\nimport type { Store } from 'redux'\n\nimport type {\n  ConnectedComponent,\n  InferableComponentEnhancer,\n  InferableComponentEnhancerWithProps,\n  ResolveThunks,\n  DispatchProp,\n  ConnectPropsMaybeWithoutContext,\n} from '../types'\n\nimport type {\n  MapStateToPropsParam,\n  MapDispatchToPropsParam,\n  MergeProps,\n  MapDispatchToPropsNonObject,\n  SelectorFactoryOptions,\n} from '../connect/selectorFactory'\nimport defaultSelectorFactory from '../connect/selectorFactory'\nimport { mapDispatchToPropsFactory } from '../connect/mapDispatchToProps'\nimport { mapStateToPropsFactory } from '../connect/mapStateToProps'\nimport { mergePropsFactory } from '../connect/mergeProps'\n\nimport type { Subscription } from '../utils/Subscription'\nimport { createSubscription } from '../utils/Subscription'\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect'\nimport shallowEqual from '../utils/shallowEqual'\nimport hoistStatics from '../utils/hoistStatics'\nimport warning from '../utils/warning'\n\nimport type {\n  ReactReduxContextValue,\n  ReactReduxContextInstance,\n} from './Context'\nimport { ReactReduxContext } from './Context'\n\n// Define some constant arrays just to avoid re-creating these\nconst EMPTY_ARRAY: [unknown, number] = [null, 0]\nconst NO_SUBSCRIPTION_ARRAY = [null, null]\n\n// Attempts to stringify whatever not-really-a-component value we were given\n// for logging in an error message\nconst stringifyComponent = (Comp: unknown) => {\n  try {\n    return JSON.stringify(Comp)\n  } catch (err) {\n    return String(Comp)\n  }\n}\n\ntype EffectFunc = (...args: any[]) => void | ReturnType<React.EffectCallback>\n\n// This is \"just\" a `useLayoutEffect`, but with two modifications:\n// - we need to fall back to `useEffect` in SSR to avoid annoying warnings\n// - we extract this to a separate function to avoid closing over values\n//   and causing memory leaks\nfunction useIsomorphicLayoutEffectWithArgs(\n  effectFunc: EffectFunc,\n  effectArgs: any[],\n  dependencies?: React.DependencyList,\n) {\n  useIsomorphicLayoutEffect(() => effectFunc(...effectArgs), dependencies)\n}\n\n// Effect callback, extracted: assign the latest props values to refs for later usage\nfunction captureWrapperProps(\n  lastWrapperProps: React.MutableRefObject<unknown>,\n  lastChildProps: React.MutableRefObject<unknown>,\n  renderIsScheduled: React.MutableRefObject<boolean>,\n  wrapperProps: unknown,\n  // actualChildProps: unknown,\n  childPropsFromStoreUpdate: React.MutableRefObject<unknown>,\n  notifyNestedSubs: () => void,\n) {\n  // We want to capture the wrapper props and child props we used for later comparisons\n  lastWrapperProps.current = wrapperProps\n  renderIsScheduled.current = false\n\n  // If the render was from a store update, clear out that reference and cascade the subscriber update\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null\n    notifyNestedSubs()\n  }\n}\n\n// Effect callback, extracted: subscribe to the Redux store or nearest connected ancestor,\n// check for updates after dispatched actions, and trigger re-renders.\nfunction subscribeUpdates(\n  shouldHandleStateChanges: boolean,\n  store: Store,\n  subscription: Subscription,\n  childPropsSelector: (state: unknown, props: unknown) => unknown,\n  lastWrapperProps: React.MutableRefObject<unknown>,\n  lastChildProps: React.MutableRefObject<unknown>,\n  renderIsScheduled: React.MutableRefObject<boolean>,\n  isMounted: React.MutableRefObject<boolean>,\n  childPropsFromStoreUpdate: React.MutableRefObject<unknown>,\n  notifyNestedSubs: () => void,\n  // forceComponentUpdateDispatch: React.Dispatch<any>,\n  additionalSubscribeListener: () => void,\n) {\n  // If we're not subscribed to the store, nothing to do here\n  if (!shouldHandleStateChanges) return () => {}\n\n  // Capture values for checking if and when this component unmounts\n  let didUnsubscribe = false\n  let lastThrownError: Error | null = null\n\n  // We'll run this callback every time a store subscription update propagates to this component\n  const checkForUpdates = () => {\n    if (didUnsubscribe || !isMounted.current) {\n      // Don't run stale listeners.\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\n      return\n    }\n\n    // TODO We're currently calling getState ourselves here, rather than letting `uSES` do it\n    const latestStoreState = store.getState()\n\n    let newChildProps, error\n    try {\n      // Actually run the selector with the most recent store state and wrapper props\n      // to determine what the child props should be\n      newChildProps = childPropsSelector(\n        latestStoreState,\n        lastWrapperProps.current,\n      )\n    } catch (e) {\n      error = e\n      lastThrownError = e as Error | null\n    }\n\n    if (!error) {\n      lastThrownError = null\n    }\n\n    // If the child props haven't changed, nothing to do here - cascade the subscription update\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs()\n      }\n    } else {\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\n      // forcing another re-render, which we don't want.\n      lastChildProps.current = newChildProps\n      childPropsFromStoreUpdate.current = newChildProps\n      renderIsScheduled.current = true\n\n      // TODO This is hacky and not how `uSES` is meant to be used\n      // Trigger the React `useSyncExternalStore` subscriber\n      additionalSubscribeListener()\n    }\n  }\n\n  // Actually subscribe to the nearest connected ancestor (or store)\n  subscription.onStateChange = checkForUpdates\n  subscription.trySubscribe()\n\n  // Pull data from the store after first render in case the store has\n  // changed since we began.\n  checkForUpdates()\n\n  const unsubscribeWrapper = () => {\n    didUnsubscribe = true\n    subscription.tryUnsubscribe()\n    subscription.onStateChange = null\n\n    if (lastThrownError) {\n      // It's possible that we caught an error due to a bad mapState function, but the\n      // parent re-rendered without this component and we're about to unmount.\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\n      // if we ever do those wrong, this throw will surface the error in our tests.\n      // In that case, throw the error from here so it doesn't get lost.\n      throw lastThrownError\n    }\n  }\n\n  return unsubscribeWrapper\n}\n\n// Reducer initial state creation for our update reducer\nconst initStateUpdates = () => EMPTY_ARRAY\n\nexport interface ConnectProps {\n  /** A custom Context instance that the component can use to access the store from an alternate Provider using that same Context instance */\n  context?: ReactReduxContextInstance\n  /** A Redux store instance to be used for subscriptions instead of the store from a Provider */\n  store?: Store\n}\n\ninterface InternalConnectProps extends ConnectProps {\n  reactReduxForwardedRef?: React.ForwardedRef<unknown>\n}\n\nfunction strictEqual(a: unknown, b: unknown) {\n  return a === b\n}\n\n/**\n * Infers the type of props that a connector will inject into a component.\n */\nexport type ConnectedProps<TConnector> =\n  TConnector extends InferableComponentEnhancerWithProps<\n    infer TInjectedProps,\n    any\n  >\n    ? unknown extends TInjectedProps\n      ? TConnector extends InferableComponentEnhancer<infer TInjectedProps>\n        ? TInjectedProps\n        : never\n      : TInjectedProps\n    : never\n\nexport interface ConnectOptions<\n  State = unknown,\n  TStateProps = {},\n  TOwnProps = {},\n  TMergedProps = {},\n> {\n  forwardRef?: boolean\n  context?: typeof ReactReduxContext\n  areStatesEqual?: (\n    nextState: State,\n    prevState: State,\n    nextOwnProps: TOwnProps,\n    prevOwnProps: TOwnProps,\n  ) => boolean\n\n  areOwnPropsEqual?: (\n    nextOwnProps: TOwnProps,\n    prevOwnProps: TOwnProps,\n  ) => boolean\n\n  areStatePropsEqual?: (\n    nextStateProps: TStateProps,\n    prevStateProps: TStateProps,\n  ) => boolean\n  areMergedPropsEqual?: (\n    nextMergedProps: TMergedProps,\n    prevMergedProps: TMergedProps,\n  ) => boolean\n}\n\n/**\n * Connects a React component to a Redux store.\n *\n * - Without arguments, just wraps the component, without changing the behavior / props\n *\n * - If 2 params are passed (3rd param, mergeProps, is skipped), default behavior\n * is to override ownProps (as stated in the docs), so what remains is everything that's\n * not a state or dispatch prop\n *\n * - When 3rd param is passed, we don't know if ownProps propagate and whether they\n * should be valid component props, because it depends on mergeProps implementation.\n * As such, it is the user's responsibility to extend ownProps interface from state or\n * dispatch props or both when applicable\n *\n * @param mapStateToProps\n * @param mapDispatchToProps\n * @param mergeProps\n * @param options\n */\nexport interface Connect<DefaultState = unknown> {\n  // tslint:disable:no-unnecessary-generics\n  (): InferableComponentEnhancer<DispatchProp>\n\n  /** mapState only */\n  <TStateProps = {}, no_dispatch = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n  ): InferableComponentEnhancerWithProps<TStateProps & DispatchProp, TOwnProps>\n\n  /** mapDispatch only (as a function) */\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<TDispatchProps, TOwnProps>\n\n  /** mapDispatch only (as an object) */\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    ResolveThunks<TDispatchProps>,\n    TOwnProps\n  >\n\n  /** mapState and mapDispatch (as a function)*/\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    TStateProps & TDispatchProps,\n    TOwnProps\n  >\n\n  /** mapState and mapDispatch (nullish) */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: null | undefined,\n  ): InferableComponentEnhancerWithProps<TStateProps, TOwnProps>\n\n  /** mapState and mapDispatch (as an object) */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    TStateProps & ResolveThunks<TDispatchProps>,\n    TOwnProps\n  >\n\n  /** mergeProps only */\n  <no_state = {}, no_dispatch = {}, TOwnProps = {}, TMergedProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: null | undefined,\n    mergeProps: MergeProps<undefined, DispatchProp, TOwnProps, TMergedProps>,\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\n\n  /** mapState and mergeProps */\n  <\n    TStateProps = {},\n    no_dispatch = {},\n    TOwnProps = {},\n    TMergedProps = {},\n    State = DefaultState,\n  >(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: null | undefined,\n    mergeProps: MergeProps<TStateProps, DispatchProp, TOwnProps, TMergedProps>,\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\n\n  /** mapDispatch (as a object) and mergeProps */\n  <no_state = {}, TDispatchProps = {}, TOwnProps = {}, TMergedProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n    mergeProps: MergeProps<undefined, TDispatchProps, TOwnProps, TMergedProps>,\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\n\n  /** mapState and options */\n  <TStateProps = {}, no_dispatch = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: null | undefined,\n    mergeProps: null | undefined,\n    options: ConnectOptions<State, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<DispatchProp & TStateProps, TOwnProps>\n\n  /** mapDispatch (as a function) and options */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\n    mergeProps: null | undefined,\n    options: ConnectOptions<{}, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<TDispatchProps, TOwnProps>\n\n  /** mapDispatch (as an object) and options*/\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}>(\n    mapStateToProps: null | undefined,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n    mergeProps: null | undefined,\n    options: ConnectOptions<{}, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    ResolveThunks<TDispatchProps>,\n    TOwnProps\n  >\n\n  /** mapState,  mapDispatch (as a function), and options */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsNonObject<TDispatchProps, TOwnProps>,\n    mergeProps: null | undefined,\n    options: ConnectOptions<State, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    TStateProps & TDispatchProps,\n    TOwnProps\n  >\n\n  /** mapState,  mapDispatch (as an object), and options */\n  <TStateProps = {}, TDispatchProps = {}, TOwnProps = {}, State = DefaultState>(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n    mergeProps: null | undefined,\n    options: ConnectOptions<State, TStateProps, TOwnProps>,\n  ): InferableComponentEnhancerWithProps<\n    TStateProps & ResolveThunks<TDispatchProps>,\n    TOwnProps\n  >\n\n  /** mapState, mapDispatch, mergeProps, and options */\n  <\n    TStateProps = {},\n    TDispatchProps = {},\n    TOwnProps = {},\n    TMergedProps = {},\n    State = DefaultState,\n  >(\n    mapStateToProps: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n    mapDispatchToProps: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n    mergeProps: MergeProps<\n      TStateProps,\n      TDispatchProps,\n      TOwnProps,\n      TMergedProps\n    >,\n    options?: ConnectOptions<State, TStateProps, TOwnProps, TMergedProps>,\n  ): InferableComponentEnhancerWithProps<TMergedProps, TOwnProps>\n  // tslint:enable:no-unnecessary-generics\n}\n\nlet hasWarnedAboutDeprecatedPureOption = false\n\n/**\n * Connects a React component to a Redux store.\n *\n * - Without arguments, just wraps the component, without changing the behavior / props\n *\n * - If 2 params are passed (3rd param, mergeProps, is skipped), default behavior\n * is to override ownProps (as stated in the docs), so what remains is everything that's\n * not a state or dispatch prop\n *\n * - When 3rd param is passed, we don't know if ownProps propagate and whether they\n * should be valid component props, because it depends on mergeProps implementation.\n * As such, it is the user's responsibility to extend ownProps interface from state or\n * dispatch props or both when applicable\n *\n * @param mapStateToProps A function that extracts values from state\n * @param mapDispatchToProps Setup for dispatching actions\n * @param mergeProps Optional callback to merge state and dispatch props together\n * @param options Options for configuring the connection\n *\n */\nfunction connect<\n  TStateProps = {},\n  TDispatchProps = {},\n  TOwnProps = {},\n  TMergedProps = {},\n  State = unknown,\n>(\n  mapStateToProps?: MapStateToPropsParam<TStateProps, TOwnProps, State>,\n  mapDispatchToProps?: MapDispatchToPropsParam<TDispatchProps, TOwnProps>,\n  mergeProps?: MergeProps<TStateProps, TDispatchProps, TOwnProps, TMergedProps>,\n  {\n    // The `pure` option has been removed, so TS doesn't like us destructuring this to check its existence.\n    // @ts-ignore\n    pure,\n    areStatesEqual = strictEqual,\n    areOwnPropsEqual = shallowEqual,\n    areStatePropsEqual = shallowEqual,\n    areMergedPropsEqual = shallowEqual,\n\n    // use React's forwardRef to expose a ref of the wrapped component\n    forwardRef = false,\n\n    // the context consumer to use\n    context = ReactReduxContext,\n  }: ConnectOptions<unknown, unknown, unknown, unknown> = {},\n): unknown {\n  if (process.env.NODE_ENV !== 'production') {\n    if (pure !== undefined && !hasWarnedAboutDeprecatedPureOption) {\n      hasWarnedAboutDeprecatedPureOption = true\n      warning(\n        'The `pure` option has been removed. `connect` is now always a \"pure/memoized\" component',\n      )\n    }\n  }\n\n  const Context = context\n\n  const initMapStateToProps = mapStateToPropsFactory(mapStateToProps)\n  const initMapDispatchToProps = mapDispatchToPropsFactory(mapDispatchToProps)\n  const initMergeProps = mergePropsFactory(mergeProps)\n\n  const shouldHandleStateChanges = Boolean(mapStateToProps)\n\n  const wrapWithConnect = <TProps,>(\n    WrappedComponent: ComponentType<TProps>,\n  ) => {\n    type WrappedComponentProps = TProps &\n      ConnectPropsMaybeWithoutContext<TProps>\n\n    if (process.env.NODE_ENV !== 'production') {\n      const isValid = /*#__PURE__*/ isValidElementType(WrappedComponent)\n      if (!isValid)\n        throw new Error(\n          `You must pass a component to the function returned by connect. Instead received ${stringifyComponent(\n            WrappedComponent,\n          )}`,\n        )\n    }\n\n    const wrappedComponentName =\n      WrappedComponent.displayName || WrappedComponent.name || 'Component'\n\n    const displayName = `Connect(${wrappedComponentName})`\n\n    const selectorFactoryOptions: SelectorFactoryOptions<\n      any,\n      any,\n      any,\n      any,\n      State\n    > = {\n      shouldHandleStateChanges,\n      displayName,\n      wrappedComponentName,\n      WrappedComponent,\n      // @ts-ignore\n      initMapStateToProps,\n      initMapDispatchToProps,\n      initMergeProps,\n      areStatesEqual,\n      areStatePropsEqual,\n      areOwnPropsEqual,\n      areMergedPropsEqual,\n    }\n\n    function ConnectFunction<TOwnProps>(\n      props: InternalConnectProps & TOwnProps,\n    ) {\n      const [propsContext, reactReduxForwardedRef, wrapperProps] =\n        React.useMemo(() => {\n          // Distinguish between actual \"data\" props that were passed to the wrapper component,\n          // and values needed to control behavior (forwarded refs, alternate context instances).\n          // To maintain the wrapperProps object reference, memoize this destructuring.\n          const { reactReduxForwardedRef, ...wrapperProps } = props\n          return [props.context, reactReduxForwardedRef, wrapperProps]\n        }, [props])\n\n      const ContextToUse: ReactReduxContextInstance = React.useMemo(() => {\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\n        // Memoize the check that determines which context instance we should use.\n        let ResultContext = Context\n        if (propsContext?.Consumer) {\n          if (process.env.NODE_ENV !== 'production') {\n            const isValid = /*#__PURE__*/ isContextConsumer(\n              // @ts-ignore\n              <propsContext.Consumer />,\n            )\n            if (!isValid) {\n              throw new Error(\n                'You must pass a valid React context consumer as `props.context`',\n              )\n            }\n            ResultContext = propsContext\n          }\n        }\n        return ResultContext\n      }, [propsContext, Context])\n\n      // Retrieve the store and ancestor subscription via context, if available\n      const contextValue = React.useContext(ContextToUse)\n\n      // The store _must_ exist as either a prop or in context.\n      // We'll check to see if it _looks_ like a Redux store first.\n      // This allows us to pass through a `store` prop that is just a plain value.\n      const didStoreComeFromProps =\n        Boolean(props.store) &&\n        Boolean(props.store!.getState) &&\n        Boolean(props.store!.dispatch)\n      const didStoreComeFromContext =\n        Boolean(contextValue) && Boolean(contextValue!.store)\n\n      if (\n        process.env.NODE_ENV !== 'production' &&\n        !didStoreComeFromProps &&\n        !didStoreComeFromContext\n      ) {\n        throw new Error(\n          `Could not find \"store\" in the context of ` +\n            `\"${displayName}\". Either wrap the root component in a <Provider>, ` +\n            `or pass a custom React context provider to <Provider> and the corresponding ` +\n            `React context consumer to ${displayName} in connect options.`,\n        )\n      }\n\n      // Based on the previous check, one of these must be true\n      const store: Store = didStoreComeFromProps\n        ? props.store!\n        : contextValue!.store\n\n      const getServerState = didStoreComeFromContext\n        ? contextValue!.getServerState\n        : store.getState\n\n      const childPropsSelector = React.useMemo(() => {\n        // The child props selector needs the store reference as an input.\n        // Re-create this selector whenever the store changes.\n        return defaultSelectorFactory(store.dispatch, selectorFactoryOptions)\n      }, [store])\n\n      const [subscription, notifyNestedSubs] = React.useMemo(() => {\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY\n\n        // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n        const subscription = createSubscription(\n          store,\n          didStoreComeFromProps ? undefined : contextValue!.subscription,\n        )\n\n        // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n        const notifyNestedSubs =\n          subscription.notifyNestedSubs.bind(subscription)\n\n        return [subscription, notifyNestedSubs]\n      }, [store, didStoreComeFromProps, contextValue])\n\n      // Determine what {store, subscription} value should be put into nested context, if necessary,\n      // and memoize that value to avoid unnecessary context updates.\n      const overriddenContextValue = React.useMemo(() => {\n        if (didStoreComeFromProps) {\n          // This component is directly subscribed to a store from props.\n          // We don't want descendants reading from this store - pass down whatever\n          // the existing context value is from the nearest connected ancestor.\n          return contextValue!\n        }\n\n        // Otherwise, put this component's subscription instance into context, so that\n        // connected descendants won't update until after this component is done\n        return {\n          ...contextValue,\n          subscription,\n        } as ReactReduxContextValue\n      }, [didStoreComeFromProps, contextValue, subscription])\n\n      // Set up refs to coordinate values between the subscription effect and the render logic\n      const lastChildProps = React.useRef<unknown>(undefined)\n      const lastWrapperProps = React.useRef(wrapperProps)\n      const childPropsFromStoreUpdate = React.useRef<unknown>(undefined)\n      const renderIsScheduled = React.useRef(false)\n      const isMounted = React.useRef(false)\n\n      // TODO: Change this to `React.useRef<Error>(undefined)` after upgrading to React 19.\n      /**\n       * @todo Change this to `React.useRef<Error>(undefined)` after upgrading to React 19.\n       */\n      const latestSubscriptionCallbackError = React.useRef<Error | undefined>(\n        undefined,\n      )\n\n      useIsomorphicLayoutEffect(() => {\n        isMounted.current = true\n        return () => {\n          isMounted.current = false\n        }\n      }, [])\n\n      const actualChildPropsSelector = React.useMemo(() => {\n        const selector = () => {\n          // Tricky logic here:\n          // - This render may have been triggered by a Redux store update that produced new child props\n          // - However, we may have gotten new wrapper props after that\n          // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\n          // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\n          // So, we'll use the child props from store update only if the wrapper props are the same as last time.\n          if (\n            childPropsFromStoreUpdate.current &&\n            wrapperProps === lastWrapperProps.current\n          ) {\n            return childPropsFromStoreUpdate.current\n          }\n\n          // TODO We're reading the store directly in render() here. Bad idea?\n          // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\n          // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\n          // to determine what the child props should be.\n          return childPropsSelector(store.getState(), wrapperProps)\n        }\n        return selector\n      }, [store, wrapperProps])\n\n      // We need this to execute synchronously every time we re-render. However, React warns\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\n      // just useEffect instead to avoid the warning, since neither will run anyway.\n\n      const subscribeForReact = React.useMemo(() => {\n        const subscribe = (reactListener: () => void) => {\n          if (!subscription) {\n            return () => {}\n          }\n\n          return subscribeUpdates(\n            shouldHandleStateChanges,\n            store,\n            subscription,\n            // @ts-ignore\n            childPropsSelector,\n            lastWrapperProps,\n            lastChildProps,\n            renderIsScheduled,\n            isMounted,\n            childPropsFromStoreUpdate,\n            notifyNestedSubs,\n            reactListener,\n          )\n        }\n\n        return subscribe\n      }, [subscription])\n\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [\n        lastWrapperProps,\n        lastChildProps,\n        renderIsScheduled,\n        wrapperProps,\n        childPropsFromStoreUpdate,\n        notifyNestedSubs,\n      ])\n\n      let actualChildProps: Record<string, unknown>\n\n      try {\n        actualChildProps = React.useSyncExternalStore(\n          // TODO We're passing through a big wrapper that does a bunch of extra side effects besides subscribing\n          subscribeForReact,\n          // TODO This is incredibly hacky. We've already processed the store update and calculated new child props,\n          // TODO and we're just passing that through so it triggers a re-render for us rather than relying on `uSES`.\n          actualChildPropsSelector,\n          getServerState\n            ? () => childPropsSelector(getServerState(), wrapperProps)\n            : actualChildPropsSelector,\n        )\n      } catch (err) {\n        if (latestSubscriptionCallbackError.current) {\n          // eslint-disable-next-line no-extra-semi\n          ;(err as Error).message +=\n            `\\nThe error may be correlated with this previous error:\\n${latestSubscriptionCallbackError.current.stack}\\n\\n`\n        }\n\n        throw err\n      }\n\n      useIsomorphicLayoutEffect(() => {\n        latestSubscriptionCallbackError.current = undefined\n        childPropsFromStoreUpdate.current = undefined\n        lastChildProps.current = actualChildProps\n      })\n\n      // Now that all that's done, we can finally try to actually render the child component.\n      // We memoize the elements for the rendered child component as an optimization.\n      const renderedWrappedComponent = React.useMemo(() => {\n        return (\n          // @ts-ignore\n          <WrappedComponent\n            {...actualChildProps}\n            ref={reactReduxForwardedRef}\n          />\n        )\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps])\n\n      // If React sees the exact same element reference as last time, it bails out of re-rendering\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\n      const renderedChild = React.useMemo(() => {\n        if (shouldHandleStateChanges) {\n          // If this component is subscribed to store updates, we need to pass its own\n          // subscription instance down to our descendants. That means rendering the same\n          // Context instance, and putting a different value into the context.\n          return (\n            <ContextToUse.Provider value={overriddenContextValue}>\n              {renderedWrappedComponent}\n            </ContextToUse.Provider>\n          )\n        }\n\n        return renderedWrappedComponent\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue])\n\n      return renderedChild\n    }\n\n    const _Connect = React.memo(ConnectFunction)\n\n    type ConnectedWrapperComponent = typeof _Connect & {\n      WrappedComponent: typeof WrappedComponent\n    }\n\n    // Add a hacky cast to get the right output type\n    const Connect = _Connect as unknown as ConnectedComponent<\n      typeof WrappedComponent,\n      WrappedComponentProps\n    >\n    Connect.WrappedComponent = WrappedComponent\n    Connect.displayName = ConnectFunction.displayName = displayName\n\n    if (forwardRef) {\n      const _forwarded = React.forwardRef(\n        function forwardConnectRef(props, ref) {\n          // @ts-ignore\n          return <Connect {...props} reactReduxForwardedRef={ref} />\n        },\n      )\n\n      const forwarded = _forwarded as ConnectedWrapperComponent\n      forwarded.displayName = displayName\n      forwarded.WrappedComponent = WrappedComponent\n      return /*#__PURE__*/ hoistStatics(forwarded, WrappedComponent)\n    }\n\n    return /*#__PURE__*/ hoistStatics(Connect, WrappedComponent)\n  }\n\n  return wrapWithConnect\n}\n\nexport default connect as Connect\n", "import type { Context, ReactNode } from 'react'\nimport { React } from '../utils/react'\nimport type { Action, Store, UnknownAction } from 'redux'\nimport type { DevModeCheckFrequency } from '../hooks/useSelector'\nimport { createSubscription } from '../utils/Subscription'\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect'\nimport type { ReactReduxContextValue } from './Context'\nimport { ReactReduxContext } from './Context'\n\nexport interface ProviderProps<\n  A extends Action<string> = UnknownAction,\n  S = unknown,\n> {\n  /**\n   * The single Redux store in your application.\n   */\n  store: Store<S, A>\n\n  /**\n   * An optional server state snapshot. Will be used during initial hydration render if available, to ensure that the UI output is consistent with the HTML generated on the server.\n   */\n  serverState?: S\n\n  /**\n   * Optional context to be used internally in react-redux. Use React.createContext() to create a context to be used.\n   * If this is used, you'll need to customize `connect` by supplying the same context provided to the Provider.\n   * Set the initial value to null, and the hooks will error\n   * if this is not overwritten by Provider.\n   */\n  context?: Context<ReactReduxContextValue<S, A> | null>\n\n  /**\n   * Determines the frequency of stability checks for all selectors.\n   * This setting overrides the global configuration for\n   * the `useSelector` stability check, allowing you to specify how often\n   * these checks should occur in development mode.\n   *\n   * @since 8.1.0\n   */\n  stabilityCheck?: DevModeCheckFrequency\n\n  /**\n   * Determines the frequency of identity function checks for all selectors.\n   * This setting overrides the global configuration for\n   * the `useSelector` identity function check, allowing you to specify how often\n   * these checks should occur in development mode.\n   *\n   * **Note**: Previously referred to as `noopCheck`.\n   *\n   * @since 9.0.0\n   */\n  identityFunctionCheck?: DevModeCheckFrequency\n\n  children: ReactNode\n}\n\nfunction Provider<A extends Action<string> = UnknownAction, S = unknown>(\n  providerProps: ProviderProps<A, S>,\n) {\n  const { children, context, serverState, store } = providerProps\n\n  const contextValue = React.useMemo(() => {\n    const subscription = createSubscription(store)\n\n    const baseContextValue = {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : undefined,\n    }\n\n    if (process.env.NODE_ENV === 'production') {\n      return baseContextValue\n    } else {\n      const { identityFunctionCheck = 'once', stabilityCheck = 'once' } =\n        providerProps\n\n      return /* @__PURE__ */ Object.assign(baseContextValue, {\n        stabilityCheck,\n        identityFunctionCheck,\n      })\n    }\n  }, [store, serverState])\n\n  const previousState = React.useMemo(() => store.getState(), [store])\n\n  useIsomorphicLayoutEffect(() => {\n    const { subscription } = contextValue\n    subscription.onStateChange = subscription.notifyNestedSubs\n    subscription.trySubscribe()\n\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs()\n    }\n    return () => {\n      subscription.tryUnsubscribe()\n      subscription.onStateChange = undefined\n    }\n  }, [contextValue, previousState])\n\n  const Context = context || ReactReduxContext\n\n  return <Context.Provider value={contextValue}>{children}</Context.Provider>\n}\n\nexport default Provider\n", "import { React } from '../utils/react'\nimport { ReactReduxContext } from '../components/Context'\nimport type { ReactReduxContextValue } from '../components/Context'\n\n/**\n * Hook factory, which creates a `useReduxContext` hook bound to a given context. This is a low-level\n * hook that you should usually not need to call directly.\n *\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\n * @returns {Function} A `useReduxContext` hook bound to the specified context.\n */\nexport function createReduxContextHook(context = ReactReduxContext) {\n  return function useReduxContext(): ReactReduxContextValue {\n    const contextValue = React.useContext(context)\n\n    if (process.env.NODE_ENV !== 'production' && !contextValue) {\n      throw new Error(\n        'could not find react-redux context value; please ensure the component is wrapped in a <Provider>',\n      )\n    }\n\n    return contextValue!\n  }\n}\n\n/**\n * A hook to access the value of the `ReactReduxContext`. This is a low-level\n * hook that you should usually not need to call directly.\n *\n * @returns {any} the value of the `ReactReduxContext`\n *\n * @example\n *\n * import React from 'react'\n * import { useReduxContext } from 'react-redux'\n *\n * export const CounterComponent = () => {\n *   const { store } = useReduxContext()\n *   return <div>{store.getState()}</div>\n * }\n */\nexport const useReduxContext = /*#__PURE__*/ createReduxContextHook()\n", "import type { Context } from 'react'\nimport type { Action, Store } from 'redux'\nimport type { ReactReduxContextValue } from '../components/Context'\nimport { ReactReduxContext } from '../components/Context'\nimport {\n  createReduxContextHook,\n  useReduxContext as useDefaultReduxContext,\n} from './useReduxContext'\n\n/**\n * Represents a type that extracts the action type from a given Redux store.\n *\n * @template StoreType - The specific type of the Redux store.\n *\n * @since 9.1.0\n * @internal\n */\nexport type ExtractStoreActionType<StoreType extends Store> =\n  StoreType extends Store<any, infer ActionType> ? ActionType : never\n\n/**\n * Represents a custom hook that provides access to the Redux store.\n *\n * @template StoreType - The specific type of the Redux store that gets returned.\n *\n * @since 9.1.0\n * @public\n */\nexport interface UseStore<StoreType extends Store> {\n  /**\n   * Returns the Redux store instance.\n   *\n   * @returns The Redux store instance.\n   */\n  (): StoreType\n\n  /**\n   * Returns the Redux store instance with specific state and action types.\n   *\n   * @returns The Redux store with the specified state and action types.\n   *\n   * @template StateType - The specific type of the state used in the store.\n   * @template ActionType - The specific type of the actions used in the store.\n   */\n  <\n    StateType extends ReturnType<StoreType['getState']> = ReturnType<\n      StoreType['getState']\n    >,\n    ActionType extends Action = ExtractStoreActionType<Store>,\n  >(): Store<StateType, ActionType>\n\n  /**\n   * Creates a \"pre-typed\" version of {@linkcode useStore useStore}\n   * where the type of the Redux `store` is predefined.\n   *\n   * This allows you to set the `store` type once, eliminating the need to\n   * specify it with every {@linkcode useStore useStore} call.\n   *\n   * @returns A pre-typed `useStore` with the store type already defined.\n   *\n   * @example\n   * ```ts\n   * export const useAppStore = useStore.withTypes<AppStore>()\n   * ```\n   *\n   * @template OverrideStoreType - The specific type of the Redux store that gets returned.\n   *\n   * @since 9.1.0\n   */\n  withTypes: <\n    OverrideStoreType extends StoreType,\n  >() => UseStore<OverrideStoreType>\n}\n\n/**\n * Hook factory, which creates a `useStore` hook bound to a given context.\n *\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\n * @returns {Function} A `useStore` hook bound to the specified context.\n */\nexport function createStoreHook<\n  StateType = unknown,\n  ActionType extends Action = Action,\n>(\n  // @ts-ignore\n  context?: Context<ReactReduxContextValue<\n    StateType,\n    ActionType\n  > | null> = ReactReduxContext,\n) {\n  const useReduxContext =\n    context === ReactReduxContext\n      ? useDefaultReduxContext\n      : // @ts-ignore\n        createReduxContextHook(context)\n  const useStore = () => {\n    const { store } = useReduxContext()\n    return store\n  }\n\n  Object.assign(useStore, {\n    withTypes: () => useStore,\n  })\n\n  return useStore as UseStore<Store<StateType, ActionType>>\n}\n\n/**\n * A hook to access the redux store.\n *\n * @returns {any} the redux store\n *\n * @example\n *\n * import React from 'react'\n * import { useStore } from 'react-redux'\n *\n * export const ExampleComponent = () => {\n *   const store = useStore()\n *   return <div>{store.getState()}</div>\n * }\n */\nexport const useStore = /*#__PURE__*/ createStoreHook()\n", "import type { Context } from 'react'\nimport type { Action, Dispatch, UnknownAction } from 'redux'\n\nimport type { ReactReduxContextValue } from '../components/Context'\nimport { ReactReduxContext } from '../components/Context'\nimport { createStoreHook, useStore as useDefaultStore } from './useStore'\n\n/**\n * Represents a custom hook that provides a dispatch function\n * from the Redux store.\n *\n * @template DispatchType - The specific type of the dispatch function.\n *\n * @since 9.1.0\n * @public\n */\nexport interface UseDispatch<\n  DispatchType extends Dispatch<UnknownAction> = Dispatch<UnknownAction>,\n> {\n  /**\n   * Returns the dispatch function from the Redux store.\n   *\n   * @returns The dispatch function from the Redux store.\n   *\n   * @template AppDispatch - The specific type of the dispatch function.\n   */\n  <AppDispatch extends DispatchType = DispatchType>(): AppDispatch\n\n  /**\n   * Creates a \"pre-typed\" version of {@linkcode useDispatch useDispatch}\n   * where the type of the `dispatch` function is predefined.\n   *\n   * This allows you to set the `dispatch` type once, eliminating the need to\n   * specify it with every {@linkcode useDispatch useDispatch} call.\n   *\n   * @returns A pre-typed `useDispatch` with the dispatch type already defined.\n   *\n   * @example\n   * ```ts\n   * export const useAppDispatch = useDispatch.withTypes<AppDispatch>()\n   * ```\n   *\n   * @template OverrideDispatchType - The specific type of the dispatch function.\n   *\n   * @since 9.1.0\n   */\n  withTypes: <\n    OverrideDispatchType extends DispatchType,\n  >() => UseDispatch<OverrideDispatchType>\n}\n\n/**\n * Hook factory, which creates a `useDispatch` hook bound to a given context.\n *\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\n * @returns {Function} A `useDispatch` hook bound to the specified context.\n */\nexport function createDispatchHook<\n  StateType = unknown,\n  ActionType extends Action = UnknownAction,\n>(\n  // @ts-ignore\n  context?: Context<ReactReduxContextValue<\n    StateType,\n    ActionType\n  > | null> = ReactReduxContext,\n) {\n  const useStore =\n    context === ReactReduxContext ? useDefaultStore : createStoreHook(context)\n\n  const useDispatch = () => {\n    const store = useStore()\n    return store.dispatch\n  }\n\n  Object.assign(useDispatch, {\n    withTypes: () => useDispatch,\n  })\n\n  return useDispatch as UseDispatch<Dispatch<ActionType>>\n}\n\n/**\n * A hook to access the redux `dispatch` function.\n *\n * @returns {any|function} redux store's `dispatch` function\n *\n * @example\n *\n * import React, { useCallback } from 'react'\n * import { useDispatch } from 'react-redux'\n *\n * export const CounterComponent = ({ value }) => {\n *   const dispatch = useDispatch()\n *   const increaseCounter = useCallback(() => dispatch({ type: 'increase-counter' }), [])\n *   return (\n *     <div>\n *       <span>{value}</span>\n *       <button onClick={increaseCounter}>Increase counter</button>\n *     </div>\n *   )\n * }\n */\nexport const useDispatch = /*#__PURE__*/ createDispatchHook()\n", "//import * as React from 'react'\nimport { React } from '../utils/react'\nimport { useSyncExternalStoreWithSelector } from 'use-sync-external-store/with-selector.js'\nimport type { ReactReduxContextValue } from '../components/Context'\nimport { ReactReduxContext } from '../components/Context'\nimport type { EqualityFn, NoInfer } from '../types'\nimport {\n  createReduxContextHook,\n  useReduxContext as useDefaultReduxContext,\n} from './useReduxContext'\n\n/**\n * The frequency of development mode checks.\n *\n * @since 8.1.0\n * @internal\n */\nexport type DevModeCheckFrequency = 'never' | 'once' | 'always'\n\n/**\n * Represents the configuration for development mode checks.\n *\n * @since 9.0.0\n * @internal\n */\nexport interface DevModeChecks {\n  /**\n   * Overrides the global stability check for the selector.\n   * - `once` - Run only the first time the selector is called.\n   * - `always` - Run every time the selector is called.\n   * - `never` - Never run the stability check.\n   *\n   * @default 'once'\n   *\n   * @since 8.1.0\n   */\n  stabilityCheck: DevModeCheckFrequency\n\n  /**\n   * Overrides the global identity function check for the selector.\n   * - `once` - Run only the first time the selector is called.\n   * - `always` - Run every time the selector is called.\n   * - `never` - Never run the identity function check.\n   *\n   * **Note**: Previously referred to as `noopCheck`.\n   *\n   * @default 'once'\n   *\n   * @since 9.0.0\n   */\n  identityFunctionCheck: DevModeCheckFrequency\n}\n\nexport interface UseSelectorOptions<Selected = unknown> {\n  equalityFn?: EqualityFn<Selected>\n\n  /**\n   * `useSelector` performs additional checks in development mode to help\n   * identify and warn about potential issues in selector behavior. This\n   * option allows you to customize the behavior of these checks per selector.\n   *\n   * @since 9.0.0\n   */\n  devModeChecks?: Partial<DevModeChecks>\n}\n\n/**\n * Represents a custom hook that allows you to extract data from the\n * Redux store state, using a selector function. The selector function\n * takes the current state as an argument and returns a part of the state\n * or some derived data. The hook also supports an optional equality\n * function or options object to customize its behavior.\n *\n * @template StateType - The specific type of state this hook operates on.\n *\n * @public\n */\nexport interface UseSelector<StateType = unknown> {\n  /**\n   * A function that takes a selector function as its first argument.\n   * The selector function is responsible for selecting a part of\n   * the Redux store's state or computing derived data.\n   *\n   * @param selector - A function that receives the current state and returns a part of the state or some derived data.\n   * @param equalityFnOrOptions - An optional equality function or options object for customizing the behavior of the selector.\n   * @returns The selected part of the state or derived data.\n   *\n   * @template TState - The specific type of state this hook operates on.\n   * @template Selected - The type of the value that the selector function will return.\n   */\n  <TState extends StateType = StateType, Selected = unknown>(\n    selector: (state: TState) => Selected,\n    equalityFnOrOptions?: EqualityFn<Selected> | UseSelectorOptions<Selected>,\n  ): Selected\n\n  /**\n   * Creates a \"pre-typed\" version of {@linkcode useSelector useSelector}\n   * where the `state` type is predefined.\n   *\n   * This allows you to set the `state` type once, eliminating the need to\n   * specify it with every {@linkcode useSelector useSelector} call.\n   *\n   * @returns A pre-typed `useSelector` with the state type already defined.\n   *\n   * @example\n   * ```ts\n   * export const useAppSelector = useSelector.withTypes<RootState>()\n   * ```\n   *\n   * @template OverrideStateType - The specific type of state this hook operates on.\n   *\n   * @since 9.1.0\n   */\n  withTypes: <\n    OverrideStateType extends StateType,\n  >() => UseSelector<OverrideStateType>\n}\n\nconst refEquality: EqualityFn<any> = (a, b) => a === b\n\n/**\n * Hook factory, which creates a `useSelector` hook bound to a given context.\n *\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\n * @returns {Function} A `useSelector` hook bound to the specified context.\n */\nexport function createSelectorHook(\n  context: React.Context<ReactReduxContextValue<\n    any,\n    any\n  > | null> = ReactReduxContext,\n): UseSelector {\n  const useReduxContext =\n    context === ReactReduxContext\n      ? useDefaultReduxContext\n      : createReduxContextHook(context)\n\n  const useSelector = <TState, Selected>(\n    selector: (state: TState) => Selected,\n    equalityFnOrOptions:\n      | EqualityFn<NoInfer<Selected>>\n      | UseSelectorOptions<NoInfer<Selected>> = {},\n  ): Selected => {\n    const { equalityFn = refEquality } =\n      typeof equalityFnOrOptions === 'function'\n        ? { equalityFn: equalityFnOrOptions }\n        : equalityFnOrOptions\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(`You must pass a selector to useSelector`)\n      }\n      if (typeof selector !== 'function') {\n        throw new Error(`You must pass a function as a selector to useSelector`)\n      }\n      if (typeof equalityFn !== 'function') {\n        throw new Error(\n          `You must pass a function as an equality function to useSelector`,\n        )\n      }\n    }\n\n    const reduxContext = useReduxContext()\n\n    const { store, subscription, getServerState } = reduxContext\n\n    const firstRun = React.useRef(true)\n\n    const wrappedSelector = React.useCallback<typeof selector>(\n      {\n        [selector.name](state: TState) {\n          const selected = selector(state)\n          if (process.env.NODE_ENV !== 'production') {\n            const { devModeChecks = {} } =\n              typeof equalityFnOrOptions === 'function'\n                ? {}\n                : equalityFnOrOptions\n            const { identityFunctionCheck, stabilityCheck } = reduxContext\n            const {\n              identityFunctionCheck: finalIdentityFunctionCheck,\n              stabilityCheck: finalStabilityCheck,\n            } = {\n              stabilityCheck,\n              identityFunctionCheck,\n              ...devModeChecks,\n            }\n            if (\n              finalStabilityCheck === 'always' ||\n              (finalStabilityCheck === 'once' && firstRun.current)\n            ) {\n              const toCompare = selector(state)\n              if (!equalityFn(selected, toCompare)) {\n                let stack: string | undefined = undefined\n                try {\n                  throw new Error()\n                } catch (e) {\n                  // eslint-disable-next-line no-extra-semi\n                  ;({ stack } = e as Error)\n                }\n                console.warn(\n                  'Selector ' +\n                    (selector.name || 'unknown') +\n                    ' returned a different result when called with the same parameters. This can lead to unnecessary rerenders.' +\n                    '\\nSelectors that return a new reference (such as an object or an array) should be memoized: https://redux.js.org/usage/deriving-data-selectors#optimizing-selectors-with-memoization',\n                  {\n                    state,\n                    selected,\n                    selected2: toCompare,\n                    stack,\n                  },\n                )\n              }\n            }\n            if (\n              finalIdentityFunctionCheck === 'always' ||\n              (finalIdentityFunctionCheck === 'once' && firstRun.current)\n            ) {\n              // @ts-ignore\n              if (selected === state) {\n                let stack: string | undefined = undefined\n                try {\n                  throw new Error()\n                } catch (e) {\n                  // eslint-disable-next-line no-extra-semi\n                  ;({ stack } = e as Error)\n                }\n                console.warn(\n                  'Selector ' +\n                    (selector.name || 'unknown') +\n                    ' returned the root state when called. This can lead to unnecessary rerenders.' +\n                    '\\nSelectors that return the entire state are almost certainly a mistake, as they will cause a rerender whenever *anything* in state changes.',\n                  { stack },\n                )\n              }\n            }\n            if (firstRun.current) firstRun.current = false\n          }\n          return selected\n        },\n      }[selector.name],\n      [selector],\n    )\n\n    const selectedState = useSyncExternalStoreWithSelector(\n      subscription.addNestedSub,\n      store.getState,\n      getServerState || store.getState,\n      wrappedSelector,\n      equalityFn,\n    )\n\n    React.useDebugValue(selectedState)\n\n    return selectedState\n  }\n\n  Object.assign(useSelector, {\n    withTypes: () => useSelector,\n  })\n\n  return useSelector as UseSelector\n}\n\n/**\n * A hook to access the redux store's state. This hook takes a selector function\n * as an argument. The selector is called with the store state.\n *\n * This hook takes an optional equality comparison function as the second parameter\n * that allows you to customize the way the selected state is compared to determine\n * whether the component needs to be re-rendered.\n *\n * @param {Function} selector the selector function\n * @param {Function=} equalityFn the function that will be used to determine equality\n *\n * @returns {any} the selected state\n *\n * @example\n *\n * import React from 'react'\n * import { useSelector } from 'react-redux'\n *\n * export const CounterComponent = () => {\n *   const counter = useSelector(state => state.counter)\n *   return <div>{counter}</div>\n * }\n */\nexport const useSelector = /*#__PURE__*/ createSelectorHook()\n", "import connect from './components/connect'\nexport type {\n  Connect,\n  ConnectProps,\n  ConnectedProps,\n} from './components/connect'\n\nimport shallowEqual from './utils/shallowEqual'\n\nimport Provider from './components/Provider'\nimport { defaultNoopBatch } from './utils/batch'\n\nexport { ReactReduxContext } from './components/Context'\nexport type { ReactReduxContextValue } from './components/Context'\n\nexport type { ProviderProps } from './components/Provider'\n\nexport type {\n  MapDispatchToProps,\n  MapDispatchToPropsFactory,\n  MapDispatchToPropsFunction,\n  MapDispatchToPropsNonObject,\n  MapDispatchToPropsParam,\n  MapStateToProps,\n  MapStateToPropsFactory,\n  MapStateToPropsParam,\n  MergeProps,\n  Selector,\n  SelectorFactory,\n} from './connect/selectorFactory'\n\nexport { createDispatchHook, useDispatch } from './hooks/useDispatch'\nexport type { UseDispatch } from './hooks/useDispatch'\n\nexport { createSelectorHook, useSelector } from './hooks/useSelector'\nexport type { UseSelector } from './hooks/useSelector'\n\nexport { createStoreHook, useStore } from './hooks/useStore'\nexport type { UseStore } from './hooks/useStore'\n\nexport type { Subscription } from './utils/Subscription'\n\nexport * from './types'\n\n/**\n * @deprecated As of React 18, batching is enabled by default for ReactDOM and React Native.\n * This is now a no-op that immediately runs the callback.\n */\nconst batch = defaultNoopBatch\n\nexport { Provider, batch, connect, shallowEqual }\n"], "mappings": ";AAAA,YAAY,WAAW;;;ACQhB,IAAM,cAA8B,sBAAM,QAAQ,WAAW,IAAI;AAExE,IAAM,qBAAqC,uBAAO;AAAA,EAChD,cAAc,+BAA+B;AAC/C;AACA,IAAM,oBAAoC,uBAAO,IAAI,cAAc;AACnE,IAAM,sBAAsC,uBAAO,IAAI,gBAAgB;AACvE,IAAM,yBAAyC,uBAAO,IAAI,mBAAmB;AAC7E,IAAM,sBAAsC,uBAAO,IAAI,gBAAgB;AACvE,IAAM,sBAAsC,uBAAO,IAAI,gBAAgB;AACvE,IAAM,qBAAqC,uBAAO,IAAI,eAAe;AACrE,IAAM,yBAAyC,uBAAO,IAAI,mBAAmB;AAC7E,IAAM,sBAAsC,uBAAO,IAAI,gBAAgB;AACvE,IAAM,2BAA2C,uBAAO;AAAA,EACtD;AACF;AACA,IAAM,kBAAkC,uBAAO,IAAI,YAAY;AAC/D,IAAM,kBAAkC,uBAAO,IAAI,YAAY;AAC/D,IAAM,uBAAuC,uBAAO,IAAI,iBAAiB;AACzE,IAAM,yBAAyC,uBAAO;AAAA,EACpD;AACF;AAEO,IAAM,aAAa;AACnB,IAAM,OAAO;AAEb,SAAS,mBAAmB,MAAgC;AACjE,SAAO,OAAO,SAAS,YACrB,OAAO,SAAS,cAChB,SAAS,uBACT,SAAS,uBACT,SAAS,0BACT,SAAS,uBACT,SAAS,4BACT,SAAS,wBACR,OAAO,SAAS,YACf,SAAS,SACR,KAAK,aAAa,mBACjB,KAAK,aAAa,mBAClB,KAAK,aAAa,sBAClB,KAAK,aAAa,uBAClB,KAAK,aAAa,0BAClB,KAAK,aAAa,0BAClB,KAAK,gBAAgB,UACvB,OACA;AACN;AAEA,SAAS,OAAO,QAAiC;AAC/C,MAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,UAAM,EAAE,SAAS,IAAI;AAErB,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,gBAAU,SAAS,OAAO,MAAO,QAAS;AAAA,UACxC,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT;AACE,oBAAU,SAAS,UAAU,OAAO,UAAW,QAAS;AAAA,cACtD,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AAAA,cACL,KAAK;AACH,uBAAO;AAAA,cACT,KAAK;AACH,uBAAO;AAAA,cACT;AACE,uBAAO;AAAA,YACX;AAAA,QACJ;AAAA,MACF,KAAK;AACH,eAAO;AAAA,IACX;AAAA,EACF;AACF;AAEO,SAAS,kBAAkB,QAAqC;AACrE,SAAO,cACH,OAAO,MAAM,MAAM,sBACnB,OAAO,MAAM,MAAM;AACzB;AAEO,SAAS,OAAO,QAAiD;AACtE,SAAO,OAAO,MAAM,MAAM;AAC5B;;;AC1Fe,SAAR,QAAyB,SAAiB;AAE/C,MAAI,OAAO,YAAY,eAAe,OAAO,QAAQ,UAAU,YAAY;AACzE,YAAQ,MAAM,OAAO;AAAA,EACvB;AAEA,MAAI;AAIF,UAAM,IAAI,MAAM,OAAO;AAAA,EAEzB,SAAS,GAAG;AAAA,EAAC;AAEf;;;AClBA,SAAS,OAAO,UAAmB,YAA0B;AAC3D,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,MAAM,wBAAwB,UAAU,cAAc;AAAA,EAClE,WACE,eAAe,qBACf,eAAe,sBACf;AACA,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,UAAU,mBAAmB,GAAG;AACxE;AAAA,QACE,oBAAoB,UAAU;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACF;AAEe,SAAR,mBACL,iBACA,oBACA,YACM;AACN,SAAO,iBAAiB,iBAAiB;AACzC,SAAO,oBAAoB,oBAAoB;AAC/C,SAAO,YAAY,YAAY;AACjC;;;ACyCA,SAAS,8BAOP,iBACA,oBACA,YACA,UACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AACF,GACA;AACA,MAAI,oBAAoB;AACxB,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,gBAAgB,YAAmB,eAA0B;AACpE,YAAQ;AACR,eAAW;AACX,iBAAa,gBAAgB,OAAO,QAAQ;AAC5C,oBAAgB,mBAAmB,UAAU,QAAQ;AACrD,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,wBAAoB;AACpB,WAAO;AAAA,EACT;AAEA,WAAS,4BAA4B;AACnC,iBAAa,gBAAgB,OAAO,QAAQ;AAE5C,QAAI,mBAAmB;AACrB,sBAAgB,mBAAmB,UAAU,QAAQ;AAEvD,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB;AACxB,QAAI,gBAAgB;AAClB,mBAAa,gBAAgB,OAAO,QAAQ;AAE9C,QAAI,mBAAmB;AACrB,sBAAgB,mBAAmB,UAAU,QAAQ;AAEvD,kBAAc,WAAW,YAAY,eAAe,QAAQ;AAC5D,WAAO;AAAA,EACT;AAEA,WAAS,iBAAiB;AACxB,UAAM,iBAAiB,gBAAgB,OAAO,QAAQ;AACtD,UAAM,oBAAoB,CAAC,mBAAmB,gBAAgB,UAAU;AACxE,iBAAa;AAEb,QAAI;AACF,oBAAc,WAAW,YAAY,eAAe,QAAQ;AAE9D,WAAO;AAAA,EACT;AAEA,WAAS,sBAAsB,WAAkB,cAAyB;AACxE,UAAM,eAAe,CAAC,iBAAiB,cAAc,QAAQ;AAC7D,UAAM,eAAe,CAAC;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,YAAQ;AACR,eAAW;AAEX,QAAI,gBAAgB,aAAc,QAAO,0BAA0B;AACnE,QAAI,aAAc,QAAO,eAAe;AACxC,QAAI,aAAc,QAAO,eAAe;AACxC,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,uBACd,WACA,cACA;AACA,WAAO,oBACH,sBAAsB,WAAW,YAAY,IAC7C,gBAAgB,WAAW,YAAY;AAAA,EAC7C;AACF;AAgDe,SAAR,0BAOL,UACA;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA,GAAG;AACL,GAOA;AACA,QAAM,kBAAkB,oBAAoB,UAAU,OAAO;AAC7D,QAAM,qBAAqB,uBAAuB,UAAU,OAAO;AACnE,QAAM,aAAa,eAAe,UAAU,OAAO;AAEnD,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,uBAAmB,iBAAiB,oBAAoB,UAAU;AAAA,EACpE;AAEA,SAAO,8BAML,iBAAiB,oBAAoB,YAAY,UAAU,OAAO;AACtE;;;AC/Oe,SAAR,mBACL,gBACA,UACyB;AACzB,QAAM,sBAA+C,CAAC;AAEtD,aAAW,OAAO,gBAAgB;AAChC,UAAM,gBAAgB,eAAe,GAAG;AACxC,QAAI,OAAO,kBAAkB,YAAY;AACvC,0BAAoB,GAAG,IAAI,IAAI,SAAS,SAAS,cAAc,GAAG,IAAI,CAAC;AAAA,IACzE;AAAA,EACF;AACA,SAAO;AACT;;;ACXe,SAAR,cAA+B,KAAc;AAClD,MAAI,OAAO,QAAQ,YAAY,QAAQ,KAAM,QAAO;AAEpD,QAAM,QAAQ,OAAO,eAAe,GAAG;AACvC,MAAI,UAAU,KAAM,QAAO;AAE3B,MAAI,YAAY;AAChB,SAAO,OAAO,eAAe,SAAS,MAAM,MAAM;AAChD,gBAAY,OAAO,eAAe,SAAS;AAAA,EAC7C;AAEA,SAAO,UAAU;AACnB;;;ACbe,SAAR,kBACL,OACA,aACA,YACA;AACA,MAAI,CAAC,cAAc,KAAK,GAAG;AACzB;AAAA,MACE,GAAG,UAAU,SAAS,WAAW,iDAAiD,KAAK;AAAA,IACzF;AAAA,EACF;AACF;;;ACGO,SAAS,uBAMd,aAOA;AACA,SAAO,SAAS,qBAAqB,UAAoB;AACvD,UAAM,WAAW,YAAY,QAAQ;AAErC,aAAS,mBAAmB;AAC1B,aAAO;AAAA,IACT;AACA,qBAAiB,oBAAoB;AACrC,WAAO;AAAA,EACT;AACF;AAUA,SAAS,qBAAqB,YAAwB;AACpD,SAAO,WAAW,oBACd,QAAQ,WAAW,iBAAiB,IACpC,WAAW,WAAW;AAC5B;AAcO,SAAS,mBACd,YACA,YACA;AACA,SAAO,SAAS,kBACd,UACA,EAAE,YAAY,GACd;AACA,UAAM,QAAQ,SAAS,gBACrB,iBACA,UACY;AACZ,aAAO,MAAM,oBACT,MAAM,WAAW,iBAAiB,QAAQ,IAC1C,MAAM,WAAW,iBAAiB,MAAS;AAAA,IACjD;AAGA,UAAM,oBAAoB;AAE1B,UAAM,aAAa,SAAS,uBAC1B,iBACA,UACY;AACZ,YAAM,aAAa;AACnB,YAAM,oBAAoB,qBAAqB,UAAU;AACzD,UAAI,QAAQ,MAAM,iBAAiB,QAAQ;AAE3C,UAAI,OAAO,UAAU,YAAY;AAC/B,cAAM,aAAa;AACnB,cAAM,oBAAoB,qBAAqB,KAAK;AACpD,gBAAQ,MAAM,iBAAiB,QAAQ;AAAA,MACzC;AAEA,UAAI,QAAQ,IAAI,aAAa;AAC3B,0BAAkB,OAAO,aAAa,UAAU;AAElD,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AACF;;;AC3GO,SAAS,wBAAwB,KAAc,MAAc;AAClE,SAAO,CACL,UACA,YACG;AACH,UAAM,IAAI;AAAA,MACR,yBAAyB,OAAO,GAAG,QAAQ,IAAI,uCAC7C,QAAQ,oBACV;AAAA,IACF;AAAA,EACF;AACF;;;ACPO,SAAS,0BACd,oBAGA;AACA,SAAO,sBAAsB,OAAO,uBAAuB,WACvD;AAAA,IAAuB,CAAC;AAAA;AAAA,MAEtB,mBAAmB,oBAAoB,QAAQ;AAAA;AAAA,EACjD,IACA,CAAC,qBACC,uBAAuB,CAAC,cAAwC;AAAA,IAC9D;AAAA,EACF,EAAE,IACF,OAAO,uBAAuB;AAAA;AAAA,IAE5B,mBAAmB,oBAAoB,oBAAoB;AAAA,MAC3D,wBAAwB,oBAAoB,oBAAoB;AAC1E;;;ACpBO,SAAS,uBACd,iBACA;AACA,SAAO,CAAC,kBACJ,uBAAuB,OAAO,CAAC,EAAE,IACjC,OAAO,oBAAoB;AAAA;AAAA,IAEzB,mBAAmB,iBAAiB,iBAAiB;AAAA,MACrD,wBAAwB,iBAAiB,iBAAiB;AAClE;;;ACPA,SAAS,kBAMP,YACA,eACA,UACc;AAEd,SAAO,EAAE,GAAG,UAAU,GAAG,YAAY,GAAG,cAAc;AACxD;AAEA,SAAS,mBAMP,YAOoE;AACpE,SAAO,SAAS,oBACd,UACA,EAAE,aAAa,oBAAoB,GACnC;AACA,QAAI,aAAa;AACjB,QAAI;AAEJ,WAAO,SAAS,gBACd,YACA,eACA,UACA;AACA,YAAM,kBAAkB,WAAW,YAAY,eAAe,QAAQ;AAEtE,UAAI,YAAY;AACd,YAAI,CAAC,oBAAoB,iBAAiB,WAAW;AACnD,wBAAc;AAAA,MAClB,OAAO;AACL,qBAAa;AACb,sBAAc;AAEd,YAAI,QAAQ,IAAI,aAAa;AAC3B,4BAAkB,aAAa,aAAa,YAAY;AAAA,MAC5D;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,SAAS,kBAMd,YACA;AACA,SAAO,CAAC,aACJ,MAAM,oBACN,OAAO,eAAe,aACpB,mBAAmB,UAAU,IAC7B,wBAAwB,YAAY,YAAY;AACxD;;;AC5EO,SAAS,iBAAiB,UAAsB;AACrD,WAAS;AACX;;;ACWA,SAAS,2BAA2B;AAClC,MAAI,QAAyB;AAC7B,MAAI,OAAwB;AAE5B,SAAO;AAAA,IACL,QAAQ;AACN,cAAQ;AACR,aAAO;AAAA,IACT;AAAA,IAEA,SAAS;AACP,uBAAM,MAAM;AACV,YAAI,WAAW;AACf,eAAO,UAAU;AACf,mBAAS,SAAS;AAClB,qBAAW,SAAS;AAAA,QACtB;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IAEA,MAAM;AACJ,YAAM,YAAwB,CAAC;AAC/B,UAAI,WAAW;AACf,aAAO,UAAU;AACf,kBAAU,KAAK,QAAQ;AACvB,mBAAW,SAAS;AAAA,MACtB;AACA,aAAO;AAAA,IACT;AAAA,IAEA,UAAU,UAAsB;AAC9B,UAAI,eAAe;AAEnB,YAAM,WAAsB,OAAO;AAAA,QACjC;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAEA,UAAI,SAAS,MAAM;AACjB,iBAAS,KAAK,OAAO;AAAA,MACvB,OAAO;AACL,gBAAQ;AAAA,MACV;AAEA,aAAO,SAAS,cAAc;AAC5B,YAAI,CAAC,gBAAgB,UAAU,KAAM;AACrC,uBAAe;AAEf,YAAI,SAAS,MAAM;AACjB,mBAAS,KAAK,OAAO,SAAS;AAAA,QAChC,OAAO;AACL,iBAAO,SAAS;AAAA,QAClB;AACA,YAAI,SAAS,MAAM;AACjB,mBAAS,KAAK,OAAO,SAAS;AAAA,QAChC,OAAO;AACL,kBAAQ,SAAS;AAAA,QACnB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAeA,IAAM,gBAAgB;AAAA,EACpB,SAAS;AAAA,EAAC;AAAA,EACV,KAAK,MAAM,CAAC;AACd;AAEO,SAAS,mBAAmB,OAAY,WAA0B;AACvE,MAAI;AACJ,MAAI,YAAgC;AAGpC,MAAI,sBAAsB;AAG1B,MAAI,iBAAiB;AAErB,WAAS,aAAa,UAAsB;AAC1C,iBAAa;AAEb,UAAM,kBAAkB,UAAU,UAAU,QAAQ;AAGpD,QAAI,UAAU;AACd,WAAO,MAAM;AACX,UAAI,CAAC,SAAS;AACZ,kBAAU;AACV,wBAAgB;AAChB,uBAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAEA,WAAS,mBAAmB;AAC1B,cAAU,OAAO;AAAA,EACnB;AAEA,WAAS,sBAAsB;AAC7B,QAAI,aAAa,eAAe;AAC9B,mBAAa,cAAc;AAAA,IAC7B;AAAA,EACF;AAEA,WAAS,eAAe;AACtB,WAAO;AAAA,EACT;AAEA,WAAS,eAAe;AACtB;AACA,QAAI,CAAC,aAAa;AAChB,oBAAc,YACV,UAAU,aAAa,mBAAmB,IAC1C,MAAM,UAAU,mBAAmB;AAEvC,kBAAY,yBAAyB;AAAA,IACvC;AAAA,EACF;AAEA,WAAS,iBAAiB;AACxB;AACA,QAAI,eAAe,wBAAwB,GAAG;AAC5C,kBAAY;AACZ,oBAAc;AACd,gBAAU,MAAM;AAChB,kBAAY;AAAA,IACd;AAAA,EACF;AAEA,WAAS,mBAAmB;AAC1B,QAAI,CAAC,gBAAgB;AACnB,uBAAiB;AACjB,mBAAa;AAAA,IACf;AAAA,EACF;AAEA,WAAS,qBAAqB;AAC5B,QAAI,gBAAgB;AAClB,uBAAiB;AACjB,qBAAe;AAAA,IACjB;AAAA,EACF;AAEA,QAAM,eAA6B;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,cAAc,MAAM;AAAA,EACtB;AAEA,SAAO;AACT;;;AC1KA,IAAM,YAAY,MAChB,CAAC,EACC,OAAO,WAAW,eAClB,OAAO,OAAO,aAAa,eAC3B,OAAO,OAAO,SAAS,kBAAkB;AAG7C,IAAM,QAAwB,0BAAU;AAWxC,IAAM,yBAAyB,MAC7B,OAAO,cAAc,eAAe,UAAU,YAAY;AAE5D,IAAM,gBAAgC,uCAAuB;AAE7D,IAAM,+BAA+B,MACnC,SAAS,gBAAgB,MAAM,kBAAkB,MAAM;AAElD,IAAM,4BACK,6CAA6B;;;ACvC/C,SAAS,GAAG,GAAY,GAAY;AAClC,MAAI,MAAM,GAAG;AACX,WAAO,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,EAC7C,OAAO;AACL,WAAO,MAAM,KAAK,MAAM;AAAA,EAC1B;AACF;AAEe,SAAR,aAA8B,MAAW,MAAW;AACzD,MAAI,GAAG,MAAM,IAAI,EAAG,QAAO;AAE3B,MACE,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACA,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,OAAO,KAAK,IAAI;AAC9B,QAAM,QAAQ,OAAO,KAAK,IAAI;AAE9B,MAAI,MAAM,WAAW,MAAM,OAAQ,QAAO;AAE1C,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QACE,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,MAAM,CAAC,CAAC,KACpD,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,GAClC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACxBA,IAAM,gBAAgB;AAAA,EACpB,mBAAmB;AAAA,EACnB,aAAa;AAAA,EACb,cAAc;AAAA,EACd,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,MAAM;AACR;AAEA,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,OAAO;AACT;AAEA,IAAM,sBAAsB;AAAA,EAC1B,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AACb;AAEA,IAAM,eAAe;AAAA,EACnB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,cAAc;AAAA,EACd,aAAa;AAAA,EACb,WAAW;AAAA,EACX,MAAM;AACR;AAEA,IAAM,eAAe;AAAA,EACnB,CAAC,UAAU,GAAG;AAAA,EACd,CAAC,IAAI,GAAG;AACV;AAEA,SAAS,WAAW,WAAgB;AAElC,MAAI,OAAO,SAAS,GAAG;AACrB,WAAO;AAAA,EACT;AAGA,SAAO,aAAa,UAAU,UAAU,CAAC,KAAK;AAChD;AAkBA,IAAM,iBAAiB,OAAO;AAC9B,IAAM,sBAAsB,OAAO;AACnC,IAAM,wBAAwB,OAAO;AACrC,IAAM,2BAA2B,OAAO;AACxC,IAAM,iBAAiB,OAAO;AAC9B,IAAM,kBAAkB,OAAO;AAEhB,SAAR,qBAOL,iBACA,iBACgD;AAChD,MAAI,OAAO,oBAAoB,UAAU;AAGvC,QAAI,iBAAiB;AACnB,YAAM,qBAAqB,eAAe,eAAe;AACzD,UAAI,sBAAsB,uBAAuB,iBAAiB;AAChE,6BAAqB,iBAAiB,kBAAkB;AAAA,MAC1D;AAAA,IACF;AAEA,QAAI,OAA4B,oBAAoB,eAAe;AAEnE,QAAI,uBAAuB;AACzB,aAAO,KAAK,OAAO,sBAAsB,eAAe,CAAC;AAAA,IAC3D;AAEA,UAAM,gBAAgB,WAAW,eAAe;AAChD,UAAM,gBAAgB,WAAW,eAAe;AAEhD,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AACpC,YAAM,MAAM,KAAK,CAAC;AAClB,UACE,CAAC,cAAc,GAAiC,KAChD,EAAE,iBAAiB,cAAc,GAAiC,MAClE,EAAE,iBAAiB,cAAc,GAAiC,IAClE;AACA,cAAM,aAAa,yBAAyB,iBAAiB,GAAG;AAChE,YAAI;AAEF,yBAAe,iBAAiB,KAAK,UAAW;AAAA,QAClD,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;AC3HA,IAAM,aAA6B,uBAAO,IAAI,qBAAqB;AACnE,IAAM,KAMJ,OAAO,eAAe,cAClB;AAAA;AAAA,EAC2F,CAAC;AAAA;AAGlG,SAAS,aAAqD;AAC5D,MAAI,CAAC,MAAM,cAAe,QAAO,CAAC;AAElC,QAAM,aAAc,GAAG,UAAU,MAAM,oBAAI,IAGzC;AACF,MAAI,cAAc,WAAW,IAAI,MAAM,aAAa;AACpD,MAAI,CAAC,aAAa;AAChB,kBAAc,MAAM;AAAA,MAClB;AAAA,IACF;AACA,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,kBAAY,cAAc;AAAA,IAC5B;AACA,eAAW,IAAI,MAAM,eAAe,WAAW;AAAA,EACjD;AACA,SAAO;AACT;AAEO,IAAM,oBAAkC,2BAAW;;;ACJ1D,IAAM,wBAAwB,CAAC,MAAM,IAAI;AAIzC,IAAM,qBAAqB,CAAC,SAAkB;AAC5C,MAAI;AACF,WAAO,KAAK,UAAU,IAAI;AAAA,EAC5B,SAAS,KAAK;AACZ,WAAO,OAAO,IAAI;AAAA,EACpB;AACF;AAQA,SAAS,kCACP,YACA,YACA,cACA;AACA,4BAA0B,MAAM,WAAW,GAAG,UAAU,GAAG,YAAY;AACzE;AAGA,SAAS,oBACP,kBACA,gBACA,mBACA,cAEA,2BACA,kBACA;AAEA,mBAAiB,UAAU;AAC3B,oBAAkB,UAAU;AAG5B,MAAI,0BAA0B,SAAS;AACrC,8BAA0B,UAAU;AACpC,qBAAiB;AAAA,EACnB;AACF;AAIA,SAAS,iBACP,0BACA,OACA,cACA,oBACA,kBACA,gBACA,mBACA,WACA,2BACA,kBAEA,6BACA;AAEA,MAAI,CAAC,yBAA0B,QAAO,MAAM;AAAA,EAAC;AAG7C,MAAI,iBAAiB;AACrB,MAAI,kBAAgC;AAGpC,QAAM,kBAAkB,MAAM;AAC5B,QAAI,kBAAkB,CAAC,UAAU,SAAS;AAGxC;AAAA,IACF;AAGA,UAAM,mBAAmB,MAAM,SAAS;AAExC,QAAI,eAAe;AACnB,QAAI;AAGF,sBAAgB;AAAA,QACd;AAAA,QACA,iBAAiB;AAAA,MACnB;AAAA,IACF,SAAS,GAAG;AACV,cAAQ;AACR,wBAAkB;AAAA,IACpB;AAEA,QAAI,CAAC,OAAO;AACV,wBAAkB;AAAA,IACpB;AAGA,QAAI,kBAAkB,eAAe,SAAS;AAC5C,UAAI,CAAC,kBAAkB,SAAS;AAC9B,yBAAiB;AAAA,MACnB;AAAA,IACF,OAAO;AAKL,qBAAe,UAAU;AACzB,gCAA0B,UAAU;AACpC,wBAAkB,UAAU;AAI5B,kCAA4B;AAAA,IAC9B;AAAA,EACF;AAGA,eAAa,gBAAgB;AAC7B,eAAa,aAAa;AAI1B,kBAAgB;AAEhB,QAAM,qBAAqB,MAAM;AAC/B,qBAAiB;AACjB,iBAAa,eAAe;AAC5B,iBAAa,gBAAgB;AAE7B,QAAI,iBAAiB;AAMnB,YAAM;AAAA,IACR;AAAA,EACF;AAEA,SAAO;AACT;AAgBA,SAAS,YAAY,GAAY,GAAY;AAC3C,SAAO,MAAM;AACf;AAmNA,IAAI,qCAAqC;AAsBzC,SAAS,QAOP,iBACA,oBACA,YACA;AAAA;AAAA;AAAA,EAGE;AAAA,EACA,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,qBAAqB;AAAA,EACrB,sBAAsB;AAAA;AAAA,EAGtB,aAAa;AAAA;AAAA,EAGb,UAAU;AACZ,IAAwD,CAAC,GAChD;AACT,MAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,QAAI,SAAS,UAAa,CAAC,oCAAoC;AAC7D,2CAAqC;AACrC;AAAA,QACE;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,QAAM,UAAU;AAEhB,QAAM,sBAAsB,uBAAuB,eAAe;AAClE,QAAM,yBAAyB,0BAA0B,kBAAkB;AAC3E,QAAM,iBAAiB,kBAAkB,UAAU;AAEnD,QAAM,2BAA2B,QAAQ,eAAe;AAExD,QAAM,kBAAkB,CACtB,qBACG;AAIH,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,YAAM,UAAwB,mCAAmB,gBAAgB;AACjE,UAAI,CAAC;AACH,cAAM,IAAI;AAAA,UACR,mFAAmF;AAAA,YACjF;AAAA,UACF,CAAC;AAAA,QACH;AAAA,IACJ;AAEA,UAAM,uBACJ,iBAAiB,eAAe,iBAAiB,QAAQ;AAE3D,UAAM,cAAc,WAAW,oBAAoB;AAEnD,UAAM,yBAMF;AAAA,MACF;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,aAAS,gBACP,OACA;AACA,YAAM,CAAC,cAAc,wBAAwB,YAAY,IACvD,MAAM,QAAQ,MAAM;AAIlB,cAAM,EAAE,wBAAAA,yBAAwB,GAAGC,cAAa,IAAI;AACpD,eAAO,CAAC,MAAM,SAASD,yBAAwBC,aAAY;AAAA,MAC7D,GAAG,CAAC,KAAK,CAAC;AAEZ,YAAM,eAA0C,MAAM,QAAQ,MAAM;AAGlE,YAAI,gBAAgB;AACpB,YAAI,cAAc,UAAU;AAC1B,cAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,kBAAM,UAAwB;AAAA;AAAA,cAE5B,oCAAC,aAAa,UAAb,IAAsB;AAAA,YACzB;AACA,gBAAI,CAAC,SAAS;AACZ,oBAAM,IAAI;AAAA,gBACR;AAAA,cACF;AAAA,YACF;AACA,4BAAgB;AAAA,UAClB;AAAA,QACF;AACA,eAAO;AAAA,MACT,GAAG,CAAC,cAAc,OAAO,CAAC;AAG1B,YAAM,eAAe,MAAM,WAAW,YAAY;AAKlD,YAAM,wBACJ,QAAQ,MAAM,KAAK,KACnB,QAAQ,MAAM,MAAO,QAAQ,KAC7B,QAAQ,MAAM,MAAO,QAAQ;AAC/B,YAAM,0BACJ,QAAQ,YAAY,KAAK,QAAQ,aAAc,KAAK;AAEtD,UACE,QAAQ,IAAI,aAAa,gBACzB,CAAC,yBACD,CAAC,yBACD;AACA,cAAM,IAAI;AAAA,UACR,6CACM,WAAW,4JAEc,WAAW;AAAA,QAC5C;AAAA,MACF;AAGA,YAAM,QAAe,wBACjB,MAAM,QACN,aAAc;AAElB,YAAM,iBAAiB,0BACnB,aAAc,iBACd,MAAM;AAEV,YAAM,qBAAqB,MAAM,QAAQ,MAAM;AAG7C,eAAO,0BAAuB,MAAM,UAAU,sBAAsB;AAAA,MACtE,GAAG,CAAC,KAAK,CAAC;AAEV,YAAM,CAAC,cAAc,gBAAgB,IAAI,MAAM,QAAQ,MAAM;AAC3D,YAAI,CAAC,yBAA0B,QAAO;AAItC,cAAMC,gBAAe;AAAA,UACnB;AAAA,UACA,wBAAwB,SAAY,aAAc;AAAA,QACpD;AAMA,cAAMC,oBACJD,cAAa,iBAAiB,KAAKA,aAAY;AAEjD,eAAO,CAACA,eAAcC,iBAAgB;AAAA,MACxC,GAAG,CAAC,OAAO,uBAAuB,YAAY,CAAC;AAI/C,YAAM,yBAAyB,MAAM,QAAQ,MAAM;AACjD,YAAI,uBAAuB;AAIzB,iBAAO;AAAA,QACT;AAIA,eAAO;AAAA,UACL,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF,GAAG,CAAC,uBAAuB,cAAc,YAAY,CAAC;AAGtD,YAAM,iBAAiB,MAAM,OAAgB,MAAS;AACtD,YAAM,mBAAmB,MAAM,OAAO,YAAY;AAClD,YAAM,4BAA4B,MAAM,OAAgB,MAAS;AACjE,YAAM,oBAAoB,MAAM,OAAO,KAAK;AAC5C,YAAM,YAAY,MAAM,OAAO,KAAK;AAMpC,YAAM,kCAAkC,MAAM;AAAA,QAC5C;AAAA,MACF;AAEA,gCAA0B,MAAM;AAC9B,kBAAU,UAAU;AACpB,eAAO,MAAM;AACX,oBAAU,UAAU;AAAA,QACtB;AAAA,MACF,GAAG,CAAC,CAAC;AAEL,YAAM,2BAA2B,MAAM,QAAQ,MAAM;AACnD,cAAM,WAAW,MAAM;AAOrB,cACE,0BAA0B,WAC1B,iBAAiB,iBAAiB,SAClC;AACA,mBAAO,0BAA0B;AAAA,UACnC;AAMA,iBAAO,mBAAmB,MAAM,SAAS,GAAG,YAAY;AAAA,QAC1D;AACA,eAAO;AAAA,MACT,GAAG,CAAC,OAAO,YAAY,CAAC;AAMxB,YAAM,oBAAoB,MAAM,QAAQ,MAAM;AAC5C,cAAM,YAAY,CAAC,kBAA8B;AAC/C,cAAI,CAAC,cAAc;AACjB,mBAAO,MAAM;AAAA,YAAC;AAAA,UAChB;AAEA,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA;AAAA;AAAA,YAEA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,YAAY,CAAC;AAEjB,wCAAkC,qBAAqB;AAAA,QACrD;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAI;AAEJ,UAAI;AACF,2BAAmB,MAAM;AAAA;AAAA,UAEvB;AAAA;AAAA;AAAA,UAGA;AAAA,UACA,iBACI,MAAM,mBAAmB,eAAe,GAAG,YAAY,IACvD;AAAA,QACN;AAAA,MACF,SAAS,KAAK;AACZ,YAAI,gCAAgC,SAAS;AAE3C;AAAC,UAAC,IAAc,WACd;AAAA;AAAA,EAA4D,gCAAgC,QAAQ,KAAK;AAAA;AAAA;AAAA,QAC7G;AAEA,cAAM;AAAA,MACR;AAEA,gCAA0B,MAAM;AAC9B,wCAAgC,UAAU;AAC1C,kCAA0B,UAAU;AACpC,uBAAe,UAAU;AAAA,MAC3B,CAAC;AAID,YAAM,2BAA2B,MAAM,QAAQ,MAAM;AACnD;AAAA;AAAA,UAEE;AAAA,YAAC;AAAA;AAAA,cACE,GAAG;AAAA,cACJ,KAAK;AAAA;AAAA,UACP;AAAA;AAAA,MAEJ,GAAG,CAAC,wBAAwB,kBAAkB,gBAAgB,CAAC;AAI/D,YAAM,gBAAgB,MAAM,QAAQ,MAAM;AACxC,YAAI,0BAA0B;AAI5B,iBACE,oCAAC,aAAa,UAAb,EAAsB,OAAO,0BAC3B,wBACH;AAAA,QAEJ;AAEA,eAAO;AAAA,MACT,GAAG,CAAC,cAAc,0BAA0B,sBAAsB,CAAC;AAEnE,aAAO;AAAA,IACT;AAEA,UAAM,WAAW,MAAM,KAAK,eAAe;AAO3C,UAAM,UAAU;AAIhB,YAAQ,mBAAmB;AAC3B,YAAQ,cAAc,gBAAgB,cAAc;AAEpD,QAAI,YAAY;AACd,YAAM,aAAa,MAAM;AAAA,QACvB,SAAS,kBAAkB,OAAO,KAAK;AAErC,iBAAO,oCAAC,WAAS,GAAG,OAAO,wBAAwB,KAAK;AAAA,QAC1D;AAAA,MACF;AAEA,YAAM,YAAY;AAClB,gBAAU,cAAc;AACxB,gBAAU,mBAAmB;AAC7B,aAAqB,qCAAa,WAAW,gBAAgB;AAAA,IAC/D;AAEA,WAAqB,qCAAa,SAAS,gBAAgB;AAAA,EAC7D;AAEA,SAAO;AACT;AAEA,IAAO,kBAAQ;;;ACpvBf,SAAS,SACP,eACA;AACA,QAAM,EAAE,UAAU,SAAS,aAAa,MAAM,IAAI;AAElD,QAAM,eAAe,MAAM,QAAQ,MAAM;AACvC,UAAM,eAAe,mBAAmB,KAAK;AAE7C,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA,gBAAgB,cAAc,MAAM,cAAc;AAAA,IACpD;AAEA,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,aAAO;AAAA,IACT,OAAO;AACL,YAAM,EAAE,wBAAwB,QAAQ,iBAAiB,OAAO,IAC9D;AAEF,aAAuB,uBAAO,OAAO,kBAAkB;AAAA,QACrD;AAAA,QACA;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,GAAG,CAAC,OAAO,WAAW,CAAC;AAEvB,QAAM,gBAAgB,MAAM,QAAQ,MAAM,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC;AAEnE,4BAA0B,MAAM;AAC9B,UAAM,EAAE,aAAa,IAAI;AACzB,iBAAa,gBAAgB,aAAa;AAC1C,iBAAa,aAAa;AAE1B,QAAI,kBAAkB,MAAM,SAAS,GAAG;AACtC,mBAAa,iBAAiB;AAAA,IAChC;AACA,WAAO,MAAM;AACX,mBAAa,eAAe;AAC5B,mBAAa,gBAAgB;AAAA,IAC/B;AAAA,EACF,GAAG,CAAC,cAAc,aAAa,CAAC;AAEhC,QAAM,UAAU,WAAW;AAE3B,SAAO,oCAAC,QAAQ,UAAR,EAAiB,OAAO,gBAAe,QAAS;AAC1D;AAEA,IAAO,mBAAQ;;;AC7FR,SAAS,uBAAuB,UAAU,mBAAmB;AAClE,SAAO,SAASC,mBAA0C;AACxD,UAAM,eAAe,MAAM,WAAW,OAAO;AAE7C,QAAI,QAAQ,IAAI,aAAa,gBAAgB,CAAC,cAAc;AAC1D,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AACF;AAkBO,IAAM,kBAAgC,uCAAuB;;;ACuC7D,SAAS,gBAKd,UAGY,mBACZ;AACA,QAAMC,mBACJ,YAAY,oBACR;AAAA;AAAA,IAEA,uBAAuB,OAAO;AAAA;AACpC,QAAMC,YAAW,MAAM;AACrB,UAAM,EAAE,MAAM,IAAID,iBAAgB;AAClC,WAAO;AAAA,EACT;AAEA,SAAO,OAAOC,WAAU;AAAA,IACtB,WAAW,MAAMA;AAAA,EACnB,CAAC;AAED,SAAOA;AACT;AAiBO,IAAM,WAAyB,gCAAgB;;;ACjE/C,SAAS,mBAKd,UAGY,mBACZ;AACA,QAAMC,YACJ,YAAY,oBAAoB,WAAkB,gBAAgB,OAAO;AAE3E,QAAMC,eAAc,MAAM;AACxB,UAAM,QAAQD,UAAS;AACvB,WAAO,MAAM;AAAA,EACf;AAEA,SAAO,OAAOC,cAAa;AAAA,IACzB,WAAW,MAAMA;AAAA,EACnB,CAAC;AAED,SAAOA;AACT;AAuBO,IAAM,cAA4B,mCAAmB;;;ACrG5D,SAAS,wCAAwC;AAoHjD,IAAM,cAA+B,CAAC,GAAG,MAAM,MAAM;AAQ9C,SAAS,mBACd,UAGY,mBACC;AACb,QAAMC,mBACJ,YAAY,oBACR,kBACA,uBAAuB,OAAO;AAEpC,QAAMC,eAAc,CAClB,UACA,sBAE4C,CAAC,MAChC;AACb,UAAM,EAAE,aAAa,YAAY,IAC/B,OAAO,wBAAwB,aAC3B,EAAE,YAAY,oBAAoB,IAClC;AACN,QAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,UAAI,CAAC,UAAU;AACb,cAAM,IAAI,MAAM,yCAAyC;AAAA,MAC3D;AACA,UAAI,OAAO,aAAa,YAAY;AAClC,cAAM,IAAI,MAAM,uDAAuD;AAAA,MACzE;AACA,UAAI,OAAO,eAAe,YAAY;AACpC,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,UAAM,eAAeD,iBAAgB;AAErC,UAAM,EAAE,OAAO,cAAc,eAAe,IAAI;AAEhD,UAAM,WAAW,MAAM,OAAO,IAAI;AAElC,UAAM,kBAAkB,MAAM;AAAA,MAC5B;AAAA,QACE,CAAC,SAAS,IAAI,EAAE,OAAe;AAC7B,gBAAM,WAAW,SAAS,KAAK;AAC/B,cAAI,QAAQ,IAAI,aAAa,cAAc;AACzC,kBAAM,EAAE,gBAAgB,CAAC,EAAE,IACzB,OAAO,wBAAwB,aAC3B,CAAC,IACD;AACN,kBAAM,EAAE,uBAAuB,eAAe,IAAI;AAClD,kBAAM;AAAA,cACJ,uBAAuB;AAAA,cACvB,gBAAgB;AAAA,YAClB,IAAI;AAAA,cACF;AAAA,cACA;AAAA,cACA,GAAG;AAAA,YACL;AACA,gBACE,wBAAwB,YACvB,wBAAwB,UAAU,SAAS,SAC5C;AACA,oBAAM,YAAY,SAAS,KAAK;AAChC,kBAAI,CAAC,WAAW,UAAU,SAAS,GAAG;AACpC,oBAAI,QAA4B;AAChC,oBAAI;AACF,wBAAM,IAAI,MAAM;AAAA,gBAClB,SAAS,GAAG;AAEV;AAAC,mBAAC,EAAE,MAAM,IAAI;AAAA,gBAChB;AACA,wBAAQ;AAAA,kBACN,eACG,SAAS,QAAQ,aAClB;AAAA,kBAEF;AAAA,oBACE;AAAA,oBACA;AAAA,oBACA,WAAW;AAAA,oBACX;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,gBACE,+BAA+B,YAC9B,+BAA+B,UAAU,SAAS,SACnD;AAEA,kBAAI,aAAa,OAAO;AACtB,oBAAI,QAA4B;AAChC,oBAAI;AACF,wBAAM,IAAI,MAAM;AAAA,gBAClB,SAAS,GAAG;AAEV;AAAC,mBAAC,EAAE,MAAM,IAAI;AAAA,gBAChB;AACA,wBAAQ;AAAA,kBACN,eACG,SAAS,QAAQ,aAClB;AAAA,kBAEF,EAAE,MAAM;AAAA,gBACV;AAAA,cACF;AAAA,YACF;AACA,gBAAI,SAAS,QAAS,UAAS,UAAU;AAAA,UAC3C;AACA,iBAAO;AAAA,QACT;AAAA,MACF,EAAE,SAAS,IAAI;AAAA,MACf,CAAC,QAAQ;AAAA,IACX;AAEA,UAAM,gBAAgB;AAAA,MACpB,aAAa;AAAA,MACb,MAAM;AAAA,MACN,kBAAkB,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,IACF;AAEA,UAAM,cAAc,aAAa;AAEjC,WAAO;AAAA,EACT;AAEA,SAAO,OAAOC,cAAa;AAAA,IACzB,WAAW,MAAMA;AAAA,EACnB,CAAC;AAED,SAAOA;AACT;AAyBO,IAAM,cAA4B,mCAAmB;;;AC7O5D,IAAM,QAAQ;", "names": ["reactReduxForwardedRef", "wrapperProps", "subscription", "notifyNestedSubs", "useReduxContext", "useReduxContext", "useStore", "useStore", "useDispatch", "useReduxContext", "useSelector"]}