import React from 'react';
import { Provider } from 'react-redux';
import { store } from './store';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Login } from './Features/LoginScreen/Login';
import { ForgotPassword } from './Features/ForgetPasswordScreen/ForgotPassword';
import { VerifyCode } from './Features/VerifyCodeScreen/VerifyCode';
import { SetPassword } from './Features/SetPasswordScreen/SetPassword';
import { WelcomeBack } from './Features/WelcomeBackScreen/WelcomeBack';
import { InitialScreen } from './Features/InitialResetPassword/InitialScreen';
import { PasswordReset } from './Features/InitialResetPassword/PasswordReset';

const Stack = createStackNavigator();

export default function App() {
  return (
    <Provider store={store}>
      <NavigationContainer>
        <Stack.Navigator
          initialRouteName="Login"
          screenOptions={{
            headerShown: false,
          }}>
          <Stack.Screen name="Login" component={Login} />
          <Stack.Screen name="ForgotPassword" component={ForgotPassword} />
          <Stack.Screen name="VerifyCode" component={VerifyCode} />
          <Stack.Screen name="SetPassword" component={SetPassword} />
          <Stack.Screen name="WelcomeBack" component={WelcomeBack} />
          <Stack.Screen name="InitialScreen" component={InitialScreen} />
          <Stack.Screen name="PasswordReset" component={PasswordReset} />
        </Stack.Navigator>
      </NavigationContainer>
    </Provider>
  );
} 